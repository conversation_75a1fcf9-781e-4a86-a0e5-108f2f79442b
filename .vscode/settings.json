{
  "i18n-ally.localesPaths": "src/renderer/src/i18n/config",
  "i18n-ally.enabledParsers": ["js"],
  "i18n-ally.enabledFrameworks": ["vue", "vue-sfc"],
  "i18n-ally.keystyle": "nested",
  "i18n-ally.displayLanguage": "zh",
  "i18n-ally.sortKeys": false,
  "i18n-ally.namespace": true,
  "i18n-ally.translate.engines": ["deepl", "google"], // 翻译器
  "i18n-ally.extract.keygenStyle": "camelCase" // 翻译字段命名样式采用驼峰
}
