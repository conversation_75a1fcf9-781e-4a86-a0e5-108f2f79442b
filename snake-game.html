<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background-color: #1a1a1a;
            font-family: 'Arial', sans-serif;
        }
        
        .game-container {
            text-align: center;
            background-color: #2d2d2d;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
        }
        
        h1 {
            color: #00ff00;
            margin-bottom: 20px;
            text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
        }
        
        .score-board {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            color: #fff;
            font-size: 18px;
        }
        
        #gameBoard {
            border: 3px solid #00ff00;
            background-color: #000;
            box-shadow: 0 0 15px rgba(0, 255, 0, 0.5);
        }
        
        .controls {
            margin-top: 20px;
            color: #ccc;
        }
        
        .controls p {
            margin: 10px 0;
        }
        
        button {
            background-color: #00ff00;
            color: #000;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        
        button:hover {
            background-color: #00cc00;
        }
        
        .game-over {
            color: #ff0000;
            font-size: 24px;
            font-weight: bold;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>贪吃蛇游戏</h1>
        <div class="score-board">
            <div>分数: <span id="score">0</span></div>
            <div>最高分: <span id="highScore">0</span></div>
        </div>
        <canvas id="gameBoard" width="400" height="400"></canvas>
        <div class="controls">
            <p>使用方向键控制蛇的移动</p>
            <p>或点击下方按钮控制</p>
            <button onclick="changeDirection('up')">↑</button><br>
            <button onclick="changeDirection('left')">←</button>
            <button onclick="changeDirection('right')">→</button><br>
            <button onclick="changeDirection('down')">↓</button>
            <br>
            <button onclick="resetGame()">重新开始</button>
        </div>
        <div id="gameOver" class="game-over" style="display: none;">
            游戏结束！按空格键重新开始
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameBoard');
        const ctx = canvas.getContext('2d');
        const scoreElement = document.getElementById('score');
        const highScoreElement = document.getElementById('highScore');
        const gameOverElement = document.getElementById('gameOver');

        // 游戏配置
        const gridSize = 20;
        const tileCount = canvas.width / gridSize;

        // 游戏状态
        let snake = [
            {x: 10, y: 10}
        ];
        let food = {};
        let dx = 0;
        let dy = 0;
        let score = 0;
        let highScore = localStorage.getItem('snakeHighScore') || 0;
        let gameRunning = false;
        let gameStarted = false;

        // 初始化游戏
        function init() {
            highScoreElement.textContent = highScore;
            generateFood();
            gameRunning = true;
            gameStarted = true;
            gameLoop();
        }

        // 生成食物
        function generateFood() {
            food = {
                x: Math.floor(Math.random() * tileCount),
                y: Math.floor(Math.random() * tileCount)
            };
            
            // 确保食物不会生成在蛇身上
            for (let segment of snake) {
                if (segment.x === food.x && segment.y === food.y) {
                    generateFood();
                    return;
                }
            }
        }

        // 游戏主循环
        function gameLoop() {
            if (!gameRunning) return;
            
            setTimeout(() => {
                clearCanvas();
                moveSnake();
                drawFood();
                drawSnake();
                checkCollision();
                gameLoop();
            }, 100);
        }

        // 清空画布
        function clearCanvas() {
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        // 移动蛇
        function moveSnake() {
            if (dx === 0 && dy === 0) return;
            
            const head = {x: snake[0].x + dx, y: snake[0].y + dy};
            snake.unshift(head);

            // 检查是否吃到食物
            if (head.x === food.x && head.y === food.y) {
                score += 10;
                scoreElement.textContent = score;
                generateFood();
            } else {
                snake.pop();
            }
        }

        // 绘制蛇
        function drawSnake() {
            ctx.fillStyle = '#00ff00';
            for (let segment of snake) {
                ctx.fillRect(segment.x * gridSize, segment.y * gridSize, gridSize - 2, gridSize - 2);
            }
        }

        // 绘制食物
        function drawFood() {
            ctx.fillStyle = '#ff0000';
            ctx.fillRect(food.x * gridSize, food.y * gridSize, gridSize - 2, gridSize - 2);
        }

        // 检查碰撞
        function checkCollision() {
            const head = snake[0];
            
            // 检查是否撞墙
            if (head.x < 0 || head.x >= tileCount || head.y < 0 || head.y >= tileCount) {
                gameOver();
                return;
            }
            
            // 检查是否撞到自己
            for (let i = 1; i < snake.length; i++) {
                if (head.x === snake[i].x && head.y === snake[i].y) {
                    gameOver();
                    return;
                }
            }
        }

        // 游戏结束
        function gameOver() {
            gameRunning = false;
            gameOverElement.style.display = 'block';
            
            // 更新最高分
            if (score > highScore) {
                highScore = score;
                localStorage.setItem('snakeHighScore', highScore);
                highScoreElement.textContent = highScore;
            }
        }

        // 重置游戏
        function resetGame() {
            snake = [{x: 10, y: 10}];
            dx = 0;
            dy = 0;
            score = 0;
            scoreElement.textContent = score;
            gameOverElement.style.display = 'none';
            generateFood();
            gameRunning = true;
            gameStarted = true;
            gameLoop();
        }

        // 改变方向
        function changeDirection(direction) {
            if (!gameStarted) {
                init();
                return;
            }
            
            if (!gameRunning) return;
            
            switch(direction) {
                case 'up':
                    if (dy !== 1) {
                        dx = 0;
                        dy = -1;
                    }
                    break;
                case 'down':
                    if (dy !== -1) {
                        dx = 0;
                        dy = 1;
                    }
                    break;
                case 'left':
                    if (dx !== 1) {
                        dx = -1;
                        dy = 0;
                    }
                    break;
                case 'right':
                    if (dx !== -1) {
                        dx = 1;
                        dy = 0;
                    }
                    break;
            }
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (!gameStarted) {
                init();
                return;
            }
            
            switch(e.key) {
                case 'ArrowUp':
                    e.preventDefault();
                    changeDirection('up');
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    changeDirection('down');
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    changeDirection('left');
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    changeDirection('right');
                    break;
                case ' ':
                    e.preventDefault();
                    if (!gameRunning) {
                        resetGame();
                    }
                    break;
            }
        });

        // 初始化画布
        clearCanvas();
        drawSnake();
        generateFood();
        drawFood();
    </script>
</body>
</html>