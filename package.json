{"name": "heygem.ai", "version": "1.0.4", "description": "An open source, affordable alternative to Heygen", "main": "./out/main/index.js", "author": "guiji.ai", "homepage": "https://heygem.ai", "scripts": {"dev": "electron-vite dev --watch", "start": "electron-vite preview", "build": "electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir --config=electron-builder.yml", "build:win": "npm run build && electron-builder --win --config=electron-builder.yml", "build:linux": "npm run build && electron-builder --linux --config=electron-builder.yml", "format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^3.0.0", "axios": "^1.7.7", "better-sqlite3": "^11.5.0", "dayjs": "^1.11.13", "electron-log": "^5.2.2", "electron-updater": "^6.1.7", "fluent-ffmpeg": "^2.1.3", "lodash-es": "^4.17.21", "pinia": "^2.2.6", "tdesign-icons-vue-next": "^0.3.3", "tdesign-vue-next": "^1.10.3", "vue-i18n": "^10.0.5", "vue-router": "^4.4.5"}, "devDependencies": {"@electron-toolkit/eslint-config": "^1.0.2", "@rushstack/eslint-patch": "^1.10.3", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "electron": "^33.0.0", "electron-builder": "^24.13.3", "electron-vite": "^2.3.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.26.0", "less": "^4.2.0", "prettier": "^3.3.2", "raw-loader": "^4.0.2", "vite": "^5.3.5", "vue": "^3.5.13"}}