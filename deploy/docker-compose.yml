networks:
  ai_network:
    driver: bridge

services:
  heygem-tts:
    image: guiji2025/fish-speech-ziming
    container_name: heygem-tts
    restart: always
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=0
      - NVIDIA_DRIVER_CAPABILITIES=compute,graphics,utility,video,display
    ports:
      - '18180:8080'
    volumes:
      - d:/heygem_data/voice/data:/code/data
    command: /bin/bash -c "/opt/conda/envs/python310/bin/python3 tools/api_server.py --listen 0.0.0.0:8080"
    networks:
      - ai_network
  heygem-asr:
    image: guiji2025/fun-asr
    container_name: heygem-asr
    restart: always
    runtime: nvidia
    privileged: true
    working_dir: /workspace/FunASR/runtime
    ports:
      - '10095:10095'
    command: sh /run.sh
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    networks:
      - ai_network
  heygem-gen-video:
    image: guiji2025/heygem.ai
    container_name: heygem-gen-video
    restart: always
    runtime: nvidia
    privileged: true
    volumes:
      - d:/heygem_data/face2face:/code/data
    environment:
      - PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
    deploy:
      resources:
        reservations:
          devices:
            - capabilities: [gpu]
    shm_size: '8g'
    ports:
      - '8383:8383'
    command: python /code/app_local.py
    networks:
      - ai_network
