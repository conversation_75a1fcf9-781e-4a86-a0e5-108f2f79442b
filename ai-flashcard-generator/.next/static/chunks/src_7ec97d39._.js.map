{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/TextInputForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Badge } from '@/components/ui/badge';\nimport { GenerateCardsRequest } from '@/types';\nimport { Loader2, FileText, Upload } from 'lucide-react';\n\ninterface TextInputFormProps {\n  onGenerate: (request: GenerateCardsRequest) => Promise<void>;\n  isLoading: boolean;\n  subjects: string[];\n}\n\nexport function TextInputForm({ onGenerate, isLoading, subjects }: TextInputFormProps) {\n  const [text, setText] = useState('');\n  const [subject, setSubject] = useState('');\n  const [customSubject, setCustomSubject] = useState('');\n  const [difficulty, setDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium');\n  const [cardCount, setCardCount] = useState(5);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!text.trim()) {\n      alert('请输入要学习的文本内容');\n      return;\n    }\n\n    const finalSubject = subject === 'custom' ? customSubject : subject;\n    if (!finalSubject.trim()) {\n      alert('请选择或输入学习主题');\n      return;\n    }\n\n    await onGenerate({\n      text: text.trim(),\n      subject: finalSubject.trim(),\n      difficulty,\n      cardCount,\n    });\n  };\n\n  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (!file) return;\n\n    const reader = new FileReader();\n    reader.onload = (event) => {\n      const content = event.target?.result as string;\n      setText(content);\n    };\n    reader.readAsText(file);\n  };\n\n  const wordCount = text.trim().split(/\\s+/).filter(word => word.length > 0).length;\n\n  return (\n    <Card className=\"w-full max-w-2xl mx-auto animate-fade-in hover-lift\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <FileText className=\"w-5 h-5\" />\n          创建学习卡片\n        </CardTitle>\n      </CardHeader>\n\n      <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* 文本输入区域 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">学习内容</label>\n            <div className=\"relative\">\n              <Textarea\n                value={text}\n                onChange={(e) => setText(e.target.value)}\n                placeholder=\"请输入要学习的文本内容，比如课程笔记、文章段落、概念定义等...\"\n                className=\"min-h-[150px] resize-none\"\n                disabled={isLoading}\n              />\n              <div className=\"absolute bottom-2 right-2 text-xs text-gray-500\">\n                {wordCount} 字\n              </div>\n            </div>\n            \n            {/* 文件上传 */}\n            <div className=\"flex items-center gap-2\">\n              <input\n                type=\"file\"\n                accept=\".txt,.md\"\n                onChange={handleFileUpload}\n                className=\"hidden\"\n                id=\"file-upload\"\n                disabled={isLoading}\n              />\n              <label\n                htmlFor=\"file-upload\"\n                className=\"flex items-center gap-1 px-3 py-1 text-sm border rounded-md cursor-pointer hover:bg-gray-50 disabled:opacity-50\"\n              >\n                <Upload className=\"w-4 h-4\" />\n                上传文本文件\n              </label>\n              <span className=\"text-xs text-gray-500\">支持 .txt, .md 格式</span>\n            </div>\n          </div>\n\n          {/* 主题选择 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">学习主题</label>\n            <Select value={subject} onValueChange={setSubject} disabled={isLoading}>\n              <SelectTrigger>\n                <SelectValue placeholder=\"选择学习主题\" />\n              </SelectTrigger>\n              <SelectContent>\n                {subjects.map((subj) => (\n                  <SelectItem key={subj} value={subj}>\n                    {subj}\n                  </SelectItem>\n                ))}\n                <SelectItem value=\"custom\">自定义主题</SelectItem>\n              </SelectContent>\n            </Select>\n            \n            {subject === 'custom' && (\n              <Input\n                value={customSubject}\n                onChange={(e) => setCustomSubject(e.target.value)}\n                placeholder=\"输入自定义主题名称\"\n                disabled={isLoading}\n              />\n            )}\n          </div>\n\n          {/* 设置选项 */}\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">难度等级</label>\n              <Select \n                value={difficulty} \n                onValueChange={(value: 'easy' | 'medium' | 'hard') => setDifficulty(value)}\n                disabled={isLoading}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"easy\">\n                    <div className=\"flex items-center gap-2\">\n                      <Badge className=\"bg-green-100 text-green-800\">简单</Badge>\n                      <span className=\"text-sm\">基础概念</span>\n                    </div>\n                  </SelectItem>\n                  <SelectItem value=\"medium\">\n                    <div className=\"flex items-center gap-2\">\n                      <Badge className=\"bg-yellow-100 text-yellow-800\">中等</Badge>\n                      <span className=\"text-sm\">理解应用</span>\n                    </div>\n                  </SelectItem>\n                  <SelectItem value=\"hard\">\n                    <div className=\"flex items-center gap-2\">\n                      <Badge className=\"bg-red-100 text-red-800\">困难</Badge>\n                      <span className=\"text-sm\">深度分析</span>\n                    </div>\n                  </SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">卡片数量</label>\n              <Select \n                value={cardCount.toString()} \n                onValueChange={(value) => setCardCount(parseInt(value))}\n                disabled={isLoading}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"3\">3 张卡片</SelectItem>\n                  <SelectItem value=\"5\">5 张卡片</SelectItem>\n                  <SelectItem value=\"8\">8 张卡片</SelectItem>\n                  <SelectItem value=\"10\">10 张卡片</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          {/* 提交按钮 */}\n          <Button\n            type=\"submit\"\n            className=\"w-full transition-all duration-200 hover:scale-105 disabled:hover:scale-100\"\n            disabled={isLoading || !text.trim() || (!subject || (subject === 'custom' && !customSubject.trim()))}\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                正在生成卡片...\n              </>\n            ) : (\n              '生成学习卡片'\n            )}\n          </Button>\n\n          {/* 提示信息 */}\n          {wordCount > 0 && (\n            <div className=\"text-sm text-gray-600 bg-blue-50 p-3 rounded-lg\">\n              <p>💡 提示：</p>\n              <ul className=\"mt-1 space-y-1 text-xs\">\n                <li>• 文本长度适中时效果最佳（100-1000字）</li>\n                <li>• 内容结构清晰有助于生成更好的问答</li>\n                <li>• 可以包含定义、概念、步骤等不同类型的知识点</li>\n              </ul>\n            </div>\n          )}\n        </form>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;;;AAVA;;;;;;;;;AAkBO,SAAS,cAAc,KAAuD;QAAvD,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAsB,GAAvD;;IAC5B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,MAAM;YACN;QACF;QAEA,MAAM,eAAe,YAAY,WAAW,gBAAgB;QAC5D,IAAI,CAAC,aAAa,IAAI,IAAI;YACxB,MAAM;YACN;QACF;QAEA,MAAM,WAAW;YACf,MAAM,KAAK,IAAI;YACf,SAAS,aAAa,IAAI;YAC1B;YACA;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;YACX;QAAb,MAAM,QAAO,kBAAA,EAAE,MAAM,CAAC,KAAK,cAAd,sCAAA,eAAgB,CAAC,EAAE;QAChC,IAAI,CAAC,MAAM;QAEX,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;gBACC;YAAhB,MAAM,WAAU,gBAAA,MAAM,MAAM,cAAZ,oCAAA,cAAc,MAAM;YACpC,QAAQ;QACV;QACA,OAAO,UAAU,CAAC;IACpB;IAEA,MAAM,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;IAEjF,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAKpC,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAsB;;;;;;8CACvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uIAAA,CAAA,WAAQ;4CACP,OAAO;4CACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACvC,aAAY;4CACZ,WAAU;4CACV,UAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;gDACZ;gDAAU;;;;;;;;;;;;;8CAKf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,UAAU;4CACV,WAAU;4CACV,IAAG;4CACH,UAAU;;;;;;sDAEZ,6LAAC;4CACC,SAAQ;4CACR,WAAU;;8DAEV,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGhC,6LAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAK5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAsB;;;;;;8CACvC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAS,eAAe;oCAAY,UAAU;;sDAC3D,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;gDACX,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,qIAAA,CAAA,aAAU;wDAAY,OAAO;kEAC3B;uDADc;;;;;8DAInB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAS;;;;;;;;;;;;;;;;;;gCAI9B,YAAY,0BACX,6LAAC,oIAAA,CAAA,QAAK;oCACJ,OAAO;oCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oCAChD,aAAY;oCACZ,UAAU;;;;;;;;;;;;sCAMhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAsB;;;;;;sDACvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,OAAO;4CACP,eAAe,CAAC,QAAsC,cAAc;4CACpE,UAAU;;8DAEV,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;wEAAC,WAAU;kFAA8B;;;;;;kFAC/C,6LAAC;wEAAK,WAAU;kFAAU;;;;;;;;;;;;;;;;;sEAG9B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;wEAAC,WAAU;kFAAgC;;;;;;kFACjD,6LAAC;wEAAK,WAAU;kFAAU;;;;;;;;;;;;;;;;;sEAG9B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAChB,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;wEAAC,WAAU;kFAA0B;;;;;;kFAC3C,6LAAC;wEAAK,WAAU;kFAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAsB;;;;;;sDACvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,OAAO,UAAU,QAAQ;4CACzB,eAAe,CAAC,QAAU,aAAa,SAAS;4CAChD,UAAU;;8DAEV,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAI;;;;;;sEACtB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAI;;;;;;sEACtB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAI;;;;;;sEACtB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO/B,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU,aAAa,CAAC,KAAK,IAAI,MAAO,CAAC,WAAY,YAAY,YAAY,CAAC,cAAc,IAAI;sCAE/F,0BACC;;kDACE,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;+CAInD;;;;;;wBAKH,YAAY,mBACX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAE;;;;;;8CACH,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;GA5MgB;KAAA", "debugId": null}}, {"offset": {"line": 1186, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/FlashcardComponent.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Flashcard } from '@/types';\nimport { RotateCcw, Eye, EyeOff, Edit, Trash2 } from 'lucide-react';\n\ninterface FlashcardComponentProps {\n  card: Flashcard;\n  showActions?: boolean;\n  onEdit?: (card: Flashcard) => void;\n  onDelete?: (id: string) => void;\n  onReview?: (id: string, correct: boolean) => void;\n}\n\nexport function FlashcardComponent({ \n  card, \n  showActions = true, \n  onEdit, \n  onDelete, \n  onReview \n}: FlashcardComponentProps) {\n  const [isFlipped, setIsFlipped] = useState(false);\n  const [showAnswer, setShowAnswer] = useState(false);\n\n  const handleFlip = () => {\n    setIsFlipped(!isFlipped);\n    setShowAnswer(!showAnswer);\n  };\n\n  const getDifficultyColor = (difficulty: string) => {\n    switch (difficulty) {\n      case 'easy': return 'bg-green-100 text-green-800';\n      case 'medium': return 'bg-yellow-100 text-yellow-800';\n      case 'hard': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getDifficultyText = (difficulty: string) => {\n    switch (difficulty) {\n      case 'easy': return '简单';\n      case 'medium': return '中等';\n      case 'hard': return '困难';\n      default: return difficulty;\n    }\n  };\n\n  return (\n    <Card className=\"w-full max-w-md mx-auto transition-all duration-300 hover:shadow-lg hover-lift animate-fade-in\">\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-center justify-between\">\n          <CardTitle className=\"text-lg font-medium\">{card.subject}</CardTitle>\n          <Badge className={getDifficultyColor(card.difficulty)}>\n            {getDifficultyText(card.difficulty)}\n          </Badge>\n        </div>\n        {card.tags.length > 0 && (\n          <div className=\"flex flex-wrap gap-1 mt-2\">\n            {card.tags.map((tag, index) => (\n              <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                {tag}\n              </Badge>\n            ))}\n          </div>\n        )}\n      </CardHeader>\n\n      <CardContent className=\"space-y-4\">\n        {/* 卡片内容区域 */}\n        <div className=\"min-h-[120px] flex items-center justify-center p-4 bg-gray-50 rounded-lg relative overflow-hidden\">\n          <div className={`transition-all duration-300 ${showAnswer ? 'animate-fade-in' : 'animate-fade-in'}`}>\n            {!showAnswer ? (\n              <div className=\"text-center\">\n                <h3 className=\"font-medium text-gray-900 mb-2\">问题</h3>\n                <p className=\"text-gray-700\">{card.question}</p>\n              </div>\n            ) : (\n              <div className=\"text-center\">\n                <h3 className=\"font-medium text-gray-900 mb-2\">答案</h3>\n                <p className=\"text-gray-700\">{card.answer}</p>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* 操作按钮 */}\n        <div className=\"flex flex-col gap-2\">\n          <Button\n            onClick={handleFlip}\n            variant=\"outline\"\n            className=\"w-full transition-all duration-200 hover:scale-105\"\n          >\n            {showAnswer ? (\n              <>\n                <EyeOff className=\"w-4 h-4 mr-2\" />\n                隐藏答案\n              </>\n            ) : (\n              <>\n                <Eye className=\"w-4 h-4 mr-2\" />\n                显示答案\n              </>\n            )}\n          </Button>\n\n          {showAnswer && onReview && (\n            <div className=\"flex gap-2 animate-fade-in\">\n              <Button\n                onClick={() => onReview(card.id, false)}\n                variant=\"outline\"\n                className=\"flex-1 text-red-600 hover:text-red-700 transition-all duration-200 hover:scale-105\"\n              >\n                答错了\n              </Button>\n              <Button\n                onClick={() => onReview(card.id, true)}\n                className=\"flex-1 bg-green-600 hover:bg-green-700 transition-all duration-200 hover:scale-105\"\n              >\n                答对了\n              </Button>\n            </div>\n          )}\n\n          {showActions && (\n            <div className=\"flex gap-2 pt-2 border-t\">\n              {onEdit && (\n                <Button\n                  onClick={() => onEdit(card)}\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"flex-1\"\n                >\n                  <Edit className=\"w-4 h-4 mr-1\" />\n                  编辑\n                </Button>\n              )}\n              {onDelete && (\n                <Button\n                  onClick={() => onDelete(card.id)}\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"flex-1 text-red-600 hover:text-red-700\"\n                >\n                  <Trash2 className=\"w-4 h-4 mr-1\" />\n                  删除\n                </Button>\n              )}\n            </div>\n          )}\n        </div>\n\n        {/* 学习统计 */}\n        {card.reviewCount > 0 && (\n          <div className=\"text-xs text-gray-500 pt-2 border-t\">\n            <div className=\"flex justify-between\">\n              <span>复习次数: {card.reviewCount}</span>\n              <span>正确率: {Math.round((card.correctCount / card.reviewCount) * 100)}%</span>\n            </div>\n            {card.lastReviewed && (\n              <div className=\"mt-1\">\n                上次复习: {card.lastReviewed.toLocaleDateString()}\n              </div>\n            )}\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;;;AAPA;;;;;;AAiBO,SAAS,mBAAmB,KAMT;QANS,EACjC,IAAI,EACJ,cAAc,IAAI,EAClB,MAAM,EACN,QAAQ,EACR,QAAQ,EACgB,GANS;;IAOjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa;QACjB,aAAa,CAAC;QACd,cAAc,CAAC;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAuB,KAAK,OAAO;;;;;;0CACxD,6LAAC,oIAAA,CAAA,QAAK;gCAAC,WAAW,mBAAmB,KAAK,UAAU;0CACjD,kBAAkB,KAAK,UAAU;;;;;;;;;;;;oBAGrC,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;wBAAI,WAAU;kCACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC,oIAAA,CAAA,QAAK;gCAAa,SAAQ;gCAAU,WAAU;0CAC5C;+BADS;;;;;;;;;;;;;;;;0BAQpB,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAW,AAAC,+BAAiF,OAAnD,aAAa,oBAAoB;sCAC7E,CAAC,2BACA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,6LAAC;wCAAE,WAAU;kDAAiB,KAAK,QAAQ;;;;;;;;;;;qDAG7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,6LAAC;wCAAE,WAAU;kDAAiB,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;kCAOjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,WAAU;0CAET,2BACC;;sDACE,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;iEAIrC;;sDACE,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;4BAMrC,cAAc,0BACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,SAAS,KAAK,EAAE,EAAE;wCACjC,SAAQ;wCACR,WAAU;kDACX;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,SAAS,KAAK,EAAE,EAAE;wCACjC,WAAU;kDACX;;;;;;;;;;;;4BAMJ,6BACC,6LAAC;gCAAI,WAAU;;oCACZ,wBACC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,OAAO;wCACtB,SAAQ;wCACR,MAAK;wCACL,WAAU;;0DAEV,6LAAC,8MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;oCAIpC,0BACC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,SAAS,KAAK,EAAE;wCAC/B,SAAQ;wCACR,MAAK;wCACL,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;oBAS5C,KAAK,WAAW,GAAG,mBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAK;4CAAO,KAAK,WAAW;;;;;;;kDAC7B,6LAAC;;4CAAK;4CAAM,KAAK,KAAK,CAAC,AAAC,KAAK,YAAY,GAAG,KAAK,WAAW,GAAI;4CAAK;;;;;;;;;;;;;4BAEtE,KAAK,YAAY,kBAChB,6LAAC;gCAAI,WAAU;;oCAAO;oCACb,KAAK,YAAY,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D;GA1JgB;KAAA", "debugId": null}}, {"offset": {"line": 1552, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/FlashcardList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Badge } from '@/components/ui/badge';\nimport { FlashcardComponent } from './FlashcardComponent';\nimport { Flashcard } from '@/types';\nimport { Search, Filter, BookOpen, Clock, TrendingUp } from 'lucide-react';\n\ninterface FlashcardListProps {\n  cards: Flashcard[];\n  onEdit: (card: Flashcard) => void;\n  onDelete: (id: string) => void;\n  onReview: (id: string, correct: boolean) => void;\n}\n\nexport function FlashcardList({ cards, onEdit, onDelete, onReview }: FlashcardListProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterSubject, setFilterSubject] = useState('all');\n  const [filterDifficulty, setFilterDifficulty] = useState('all');\n  const [sortBy, setSortBy] = useState<'created' | 'reviewed' | 'accuracy'>('created');\n\n  // 获取所有主题\n  const subjects = Array.from(new Set(cards.map(card => card.subject)));\n\n  // 过滤和排序卡片\n  const filteredAndSortedCards = cards\n    .filter(card => {\n      const matchesSearch = card.question.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           card.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           card.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));\n      \n      const matchesSubject = filterSubject === 'all' || card.subject === filterSubject;\n      const matchesDifficulty = filterDifficulty === 'all' || card.difficulty === filterDifficulty;\n      \n      return matchesSearch && matchesSubject && matchesDifficulty;\n    })\n    .sort((a, b) => {\n      switch (sortBy) {\n        case 'created':\n          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n        case 'reviewed':\n          const aReviewed = a.lastReviewed?.getTime() || 0;\n          const bReviewed = b.lastReviewed?.getTime() || 0;\n          return bReviewed - aReviewed;\n        case 'accuracy':\n          const aAccuracy = a.reviewCount > 0 ? a.correctCount / a.reviewCount : 0;\n          const bAccuracy = b.reviewCount > 0 ? b.correctCount / b.reviewCount : 0;\n          return bAccuracy - aAccuracy;\n        default:\n          return 0;\n      }\n    });\n\n  // 统计信息\n  const stats = {\n    total: cards.length,\n    reviewed: cards.filter(card => card.reviewCount > 0).length,\n    needReview: cards.filter(card => {\n      if (!card.lastReviewed) return true;\n      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);\n      return card.lastReviewed < oneDayAgo;\n    }).length,\n  };\n\n  if (cards.length === 0) {\n    return (\n      <Card className=\"w-full max-w-2xl mx-auto\">\n        <CardContent className=\"flex flex-col items-center justify-center py-12\">\n          <BookOpen className=\"w-12 h-12 text-gray-400 mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">还没有学习卡片</h3>\n          <p className=\"text-gray-500 text-center\">\n            输入学习内容并生成你的第一张学习卡片吧！\n          </p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"w-full max-w-4xl mx-auto space-y-6\">\n      {/* 统计信息 */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n        <Card className=\"hover-lift animate-fade-in\">\n          <CardContent className=\"flex items-center p-4\">\n            <BookOpen className=\"w-8 h-8 text-blue-500 mr-3\" />\n            <div>\n              <p className=\"text-2xl font-bold\">{stats.total}</p>\n              <p className=\"text-sm text-gray-600\">总卡片数</p>\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"flex items-center p-4\">\n            <TrendingUp className=\"w-8 h-8 text-green-500 mr-3\" />\n            <div>\n              <p className=\"text-2xl font-bold\">{stats.reviewed}</p>\n              <p className=\"text-sm text-gray-600\">已复习</p>\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"flex items-center p-4\">\n            <Clock className=\"w-8 h-8 text-orange-500 mr-3\" />\n            <div>\n              <p className=\"text-2xl font-bold\">{stats.needReview}</p>\n              <p className=\"text-sm text-gray-600\">待复习</p>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* 搜索和过滤 */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Filter className=\"w-5 h-5\" />\n            筛选和搜索\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          {/* 搜索框 */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n            <Input\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              placeholder=\"搜索问题、答案或标签...\"\n              className=\"pl-10\"\n            />\n          </div>\n\n          {/* 过滤选项 */}\n          <div className=\"grid grid-cols-3 gap-4\">\n            <div>\n              <label className=\"text-sm font-medium mb-2 block\">主题</label>\n              <Select value={filterSubject} onValueChange={setFilterSubject}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">全部主题</SelectItem>\n                  {subjects.map(subject => (\n                    <SelectItem key={subject} value={subject}>\n                      {subject}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div>\n              <label className=\"text-sm font-medium mb-2 block\">难度</label>\n              <Select value={filterDifficulty} onValueChange={setFilterDifficulty}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">全部难度</SelectItem>\n                  <SelectItem value=\"easy\">简单</SelectItem>\n                  <SelectItem value=\"medium\">中等</SelectItem>\n                  <SelectItem value=\"hard\">困难</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div>\n              <label className=\"text-sm font-medium mb-2 block\">排序</label>\n              <Select value={sortBy} onValueChange={(value: 'created' | 'reviewed' | 'accuracy') => setSortBy(value)}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"created\">创建时间</SelectItem>\n                  <SelectItem value=\"reviewed\">复习时间</SelectItem>\n                  <SelectItem value=\"accuracy\">正确率</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 卡片列表 */}\n      <div className=\"space-y-4\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-medium\">\n            学习卡片 ({filteredAndSortedCards.length})\n          </h3>\n          {searchTerm && (\n            <Badge variant=\"outline\">\n              搜索: \"{searchTerm}\"\n            </Badge>\n          )}\n        </div>\n\n        {filteredAndSortedCards.length === 0 ? (\n          <Card>\n            <CardContent className=\"flex flex-col items-center justify-center py-8\">\n              <Search className=\"w-8 h-8 text-gray-400 mb-2\" />\n              <p className=\"text-gray-500\">没有找到匹配的卡片</p>\n            </CardContent>\n          </Card>\n        ) : (\n          <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n            {filteredAndSortedCards.map(card => (\n              <FlashcardComponent\n                key={card.id}\n                card={card}\n                onEdit={onEdit}\n                onDelete={onDelete}\n                onReview={onReview}\n              />\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;AAmBO,SAAS,cAAc,KAAyD;QAAzD,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAsB,GAAzD;;IAC5B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC;IAE1E,SAAS;IACT,MAAM,WAAW,MAAM,IAAI,CAAC,IAAI,IAAI,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO;IAElE,UAAU;IACV,MAAM,yBAAyB,MAC5B,MAAM,CAAC,CAAA;QACN,MAAM,gBAAgB,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,KAAK,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE5F,MAAM,iBAAiB,kBAAkB,SAAS,KAAK,OAAO,KAAK;QACnE,MAAM,oBAAoB,qBAAqB,SAAS,KAAK,UAAU,KAAK;QAE5E,OAAO,iBAAiB,kBAAkB;IAC5C,GACC,IAAI,CAAC,CAAC,GAAG;QACR,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YACxE,KAAK;oBACe,iBACA;gBADlB,MAAM,YAAY,EAAA,kBAAA,EAAE,YAAY,cAAd,sCAAA,gBAAgB,OAAO,OAAM;gBAC/C,MAAM,YAAY,EAAA,kBAAA,EAAE,YAAY,cAAd,sCAAA,gBAAgB,OAAO,OAAM;gBAC/C,OAAO,YAAY;YACrB,KAAK;gBACH,MAAM,YAAY,EAAE,WAAW,GAAG,IAAI,EAAE,YAAY,GAAG,EAAE,WAAW,GAAG;gBACvE,MAAM,YAAY,EAAE,WAAW,GAAG,IAAI,EAAE,YAAY,GAAG,EAAE,WAAW,GAAG;gBACvE,OAAO,YAAY;YACrB;gBACE,OAAO;QACX;IACF;IAEF,OAAO;IACP,MAAM,QAAQ;QACZ,OAAO,MAAM,MAAM;QACnB,UAAU,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,GAAG,GAAG,MAAM;QAC3D,YAAY,MAAM,MAAM,CAAC,CAAA;YACvB,IAAI,CAAC,KAAK,YAAY,EAAE,OAAO;YAC/B,MAAM,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK;YACvD,OAAO,KAAK,YAAY,GAAG;QAC7B,GAAG,MAAM;IACX;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAA4B;;;;;;;;;;;;;;;;;IAMjD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAsB,MAAM,KAAK;;;;;;sDAC9C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAK3C,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAsB,MAAM,QAAQ;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAK3C,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAsB,MAAM,UAAU;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIlC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAe,eAAe;;kEAC3C,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;4DACvB,SAAS,GAAG,CAAC,CAAA,wBACZ,6LAAC,qIAAA,CAAA,aAAU;oEAAe,OAAO;8EAC9B;mEADc;;;;;;;;;;;;;;;;;;;;;;;kDAQzB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAkB,eAAe;;kEAC9C,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAO;;;;;;0EACzB,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAO;;;;;;;;;;;;;;;;;;;;;;;;kDAK/B,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAClD,6LAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAQ,eAAe,CAAC,QAA+C,UAAU;;kEAC9F,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAsB;oCAC3B,uBAAuB,MAAM;oCAAC;;;;;;;4BAEtC,4BACC,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;;oCAAU;oCACjB;oCAAW;;;;;;;;;;;;;oBAKtB,uBAAuB,MAAM,KAAK,kBACjC,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;6CAIjC,6LAAC;wBAAI,WAAU;kCACZ,uBAAuB,GAAG,CAAC,CAAA,qBAC1B,6LAAC,2IAAA,CAAA,qBAAkB;gCAEjB,MAAM;gCACN,QAAQ;gCACR,UAAU;gCACV,UAAU;+BAJL,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;AAY5B;GA7MgB;KAAA", "debugId": null}}, {"offset": {"line": 2201, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,KAEgC;QAFhC,EACrB,GAAG,OACkD,GAFhC;IAGrB,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAGgC;QAHhC,EACrB,SAAS,EACT,GAAG,OACkD,GAHhC;IAIrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 2409, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/EditCardDialog.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Badge } from '@/components/ui/badge';\nimport { Flashcard } from '@/types';\nimport { X } from 'lucide-react';\n\ninterface EditCardDialogProps {\n  card: Flashcard | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onSave: (card: Flashcard) => void;\n  subjects: string[];\n}\n\nexport function EditCardDialog({ card, isOpen, onClose, onSave, subjects }: EditCardDialogProps) {\n  const [question, setQuestion] = useState('');\n  const [answer, setAnswer] = useState('');\n  const [subject, setSubject] = useState('');\n  const [customSubject, setCustomSubject] = useState('');\n  const [difficulty, setDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium');\n  const [tags, setTags] = useState<string[]>([]);\n  const [newTag, setNewTag] = useState('');\n\n  // 当卡片变化时更新表单\n  useEffect(() => {\n    if (card) {\n      setQuestion(card.question);\n      setAnswer(card.answer);\n      setSubject(subjects.includes(card.subject) ? card.subject : 'custom');\n      setCustomSubject(subjects.includes(card.subject) ? '' : card.subject);\n      setDifficulty(card.difficulty);\n      setTags(card.tags);\n    }\n  }, [card, subjects]);\n\n  const handleSave = () => {\n    if (!card || !question.trim() || !answer.trim()) return;\n\n    const finalSubject = subject === 'custom' ? customSubject : subject;\n    if (!finalSubject.trim()) return;\n\n    const updatedCard: Flashcard = {\n      ...card,\n      question: question.trim(),\n      answer: answer.trim(),\n      subject: finalSubject.trim(),\n      difficulty,\n      tags: tags.filter(tag => tag.trim().length > 0),\n    };\n\n    onSave(updatedCard);\n    onClose();\n  };\n\n  const handleAddTag = () => {\n    if (newTag.trim() && !tags.includes(newTag.trim())) {\n      setTags([...tags, newTag.trim()]);\n      setNewTag('');\n    }\n  };\n\n  const handleRemoveTag = (tagToRemove: string) => {\n    setTags(tags.filter(tag => tag !== tagToRemove));\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      handleAddTag();\n    }\n  };\n\n  if (!card) return null;\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle>编辑学习卡片</DialogTitle>\n        </DialogHeader>\n\n        <div className=\"space-y-6\">\n          {/* 问题 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">问题</label>\n            <Textarea\n              value={question}\n              onChange={(e) => setQuestion(e.target.value)}\n              placeholder=\"输入问题内容...\"\n              className=\"min-h-[80px]\"\n            />\n          </div>\n\n          {/* 答案 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">答案</label>\n            <Textarea\n              value={answer}\n              onChange={(e) => setAnswer(e.target.value)}\n              placeholder=\"输入答案内容...\"\n              className=\"min-h-[80px]\"\n            />\n          </div>\n\n          {/* 主题和难度 */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">主题</label>\n              <Select value={subject} onValueChange={setSubject}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  {subjects.map((subj) => (\n                    <SelectItem key={subj} value={subj}>\n                      {subj}\n                    </SelectItem>\n                  ))}\n                  <SelectItem value=\"custom\">自定义主题</SelectItem>\n                </SelectContent>\n              </Select>\n              \n              {subject === 'custom' && (\n                <Input\n                  value={customSubject}\n                  onChange={(e) => setCustomSubject(e.target.value)}\n                  placeholder=\"输入自定义主题名称\"\n                />\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">难度</label>\n              <Select \n                value={difficulty} \n                onValueChange={(value: 'easy' | 'medium' | 'hard') => setDifficulty(value)}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"easy\">简单</SelectItem>\n                  <SelectItem value=\"medium\">中等</SelectItem>\n                  <SelectItem value=\"hard\">困难</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          {/* 标签 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">标签</label>\n            \n            {/* 现有标签 */}\n            {tags.length > 0 && (\n              <div className=\"flex flex-wrap gap-2 mb-2\">\n                {tags.map((tag, index) => (\n                  <Badge key={index} variant=\"secondary\" className=\"flex items-center gap-1\">\n                    {tag}\n                    <button\n                      onClick={() => handleRemoveTag(tag)}\n                      className=\"ml-1 hover:text-red-600\"\n                    >\n                      <X className=\"w-3 h-3\" />\n                    </button>\n                  </Badge>\n                ))}\n              </div>\n            )}\n\n            {/* 添加新标签 */}\n            <div className=\"flex gap-2\">\n              <Input\n                value={newTag}\n                onChange={(e) => setNewTag(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"输入标签名称...\"\n                className=\"flex-1\"\n              />\n              <Button\n                type=\"button\"\n                onClick={handleAddTag}\n                variant=\"outline\"\n                disabled={!newTag.trim() || tags.includes(newTag.trim())}\n              >\n                添加\n              </Button>\n            </div>\n          </div>\n\n          {/* 卡片统计信息 */}\n          <div className=\"bg-gray-50 p-3 rounded-lg\">\n            <div className=\"text-sm text-gray-600 space-y-1\">\n              <div>创建时间: {card.createdAt.toLocaleString()}</div>\n              <div>复习次数: {card.reviewCount}</div>\n              {card.reviewCount > 0 && (\n                <div>正确率: {Math.round((card.correctCount / card.reviewCount) * 100)}%</div>\n              )}\n              {card.lastReviewed && (\n                <div>上次复习: {card.lastReviewed.toLocaleString()}</div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        <DialogFooter>\n          <Button variant=\"outline\" onClick={onClose}>\n            取消\n          </Button>\n          <Button \n            onClick={handleSave}\n            disabled={!question.trim() || !answer.trim() || (!subject || (subject === 'custom' && !customSubject.trim()))}\n          >\n            保存修改\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAVA;;;;;;;;;AAoBO,SAAS,eAAe,KAAgE;QAAhE,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAuB,GAAhE;;IAC7B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACzE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,aAAa;IACb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,MAAM;gBACR,YAAY,KAAK,QAAQ;gBACzB,UAAU,KAAK,MAAM;gBACrB,WAAW,SAAS,QAAQ,CAAC,KAAK,OAAO,IAAI,KAAK,OAAO,GAAG;gBAC5D,iBAAiB,SAAS,QAAQ,CAAC,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO;gBACpE,cAAc,KAAK,UAAU;gBAC7B,QAAQ,KAAK,IAAI;YACnB;QACF;mCAAG;QAAC;QAAM;KAAS;IAEnB,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI;QAEjD,MAAM,eAAe,YAAY,WAAW,gBAAgB;QAC5D,IAAI,CAAC,aAAa,IAAI,IAAI;QAE1B,MAAM,cAAyB;YAC7B,GAAG,IAAI;YACP,UAAU,SAAS,IAAI;YACvB,QAAQ,OAAO,IAAI;YACnB,SAAS,aAAa,IAAI;YAC1B;YACA,MAAM,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,GAAG,MAAM,GAAG;QAC/C;QAEA,OAAO;QACP;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,OAAO,IAAI,MAAM,CAAC,KAAK,QAAQ,CAAC,OAAO,IAAI,KAAK;YAClD,QAAQ;mBAAI;gBAAM,OAAO,IAAI;aAAG;YAChC,UAAU;QACZ;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,QAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,QAAQ;IACrC;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB;QACF;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAGf,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAsB;;;;;;8CACvC,6LAAC,uIAAA,CAAA,WAAQ;oCACP,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAsB;;;;;;8CACvC,6LAAC,uIAAA,CAAA,WAAQ;oCACP,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAsB;;;;;;sDACvC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAS,eAAe;;8DACrC,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,6LAAC,qIAAA,CAAA,gBAAa;;wDACX,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,qIAAA,CAAA,aAAU;gEAAY,OAAO;0EAC3B;+DADc;;;;;sEAInB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;;;;;;;;;;;;;wCAI9B,YAAY,0BACX,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAChD,aAAY;;;;;;;;;;;;8CAKlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAsB;;;;;;sDACvC,6LAAC,qIAAA,CAAA,SAAM;4CACL,OAAO;4CACP,eAAe,CAAC,QAAsC,cAAc;;8DAEpE,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAsB;;;;;;gCAGtC,KAAK,MAAM,GAAG,mBACb,6LAAC;oCAAI,WAAU;8CACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC,oIAAA,CAAA,QAAK;4CAAa,SAAQ;4CAAY,WAAU;;gDAC9C;8DACD,6LAAC;oDACC,SAAS,IAAM,gBAAgB;oDAC/B,WAAU;8DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;;2CANL;;;;;;;;;;8CAclB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,YAAY;4CACZ,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS;4CACT,SAAQ;4CACR,UAAU,CAAC,OAAO,IAAI,MAAM,KAAK,QAAQ,CAAC,OAAO,IAAI;sDACtD;;;;;;;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAI;4CAAO,KAAK,SAAS,CAAC,cAAc;;;;;;;kDACzC,6LAAC;;4CAAI;4CAAO,KAAK,WAAW;;;;;;;oCAC3B,KAAK,WAAW,GAAG,mBAClB,6LAAC;;4CAAI;4CAAM,KAAK,KAAK,CAAC,AAAC,KAAK,YAAY,GAAG,KAAK,WAAW,GAAI;4CAAK;;;;;;;oCAErE,KAAK,YAAY,kBAChB,6LAAC;;4CAAI;4CAAO,KAAK,YAAY,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;8BAMpD,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;sCAAS;;;;;;sCAG5C,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC,SAAS,IAAI,MAAM,CAAC,OAAO,IAAI,MAAO,CAAC,WAAY,YAAY,YAAY,CAAC,cAAc,IAAI;sCAC1G;;;;;;;;;;;;;;;;;;;;;;;AAOX;GA7MgB;KAAA", "debugId": null}}, {"offset": {"line": 2913, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,KAIoC;QAJpC,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD,GAJpC;IAKhB,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,AAAC,eAAiC,OAAnB,MAAM,CAAC,SAAS,CAAC,GAAE;YAAI;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 2959, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/ReviewMode.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Progress } from '@/components/ui/progress';\nimport { Badge } from '@/components/ui/badge';\nimport { FlashcardComponent } from './FlashcardComponent';\nimport { Flashcard } from '@/types';\nimport { RotateCcw, CheckCircle, XCircle, Clock, Target } from 'lucide-react';\n\ninterface ReviewModeProps {\n  cards: Flashcard[];\n  onReview: (id: string, correct: boolean) => void;\n  onComplete: (results: ReviewResults) => void;\n  onExit: () => void;\n}\n\ninterface ReviewResults {\n  totalCards: number;\n  correctAnswers: number;\n  incorrectAnswers: number;\n  accuracy: number;\n  timeSpent: number;\n}\n\nexport function ReviewMode({ cards, onReview, onComplete, onExit }: ReviewModeProps) {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [reviewedCards, setReviewedCards] = useState<Set<string>>(new Set());\n  const [correctCards, setCorrectCards] = useState<Set<string>>(new Set());\n  const [startTime] = useState(Date.now());\n  const [sessionCards, setSessionCards] = useState<Flashcard[]>([]);\n\n  // 初始化复习卡片（优先显示需要复习的卡片）\n  useEffect(() => {\n    const now = new Date();\n    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n    \n    // 按优先级排序：未复习过的 > 很久没复习的 > 最近复习过的\n    const sortedCards = [...cards].sort((a, b) => {\n      if (!a.lastReviewed && !b.lastReviewed) return 0;\n      if (!a.lastReviewed) return -1;\n      if (!b.lastReviewed) return 1;\n      \n      const aAccuracy = a.reviewCount > 0 ? a.correctCount / a.reviewCount : 0;\n      const bAccuracy = b.reviewCount > 0 ? b.correctCount / b.reviewCount : 0;\n      \n      // 准确率低的优先\n      if (aAccuracy !== bAccuracy) return aAccuracy - bAccuracy;\n      \n      // 最后复习时间早的优先\n      return a.lastReviewed.getTime() - b.lastReviewed.getTime();\n    });\n    \n    setSessionCards(sortedCards);\n  }, [cards]);\n\n  const currentCard = sessionCards[currentIndex];\n  const progress = sessionCards.length > 0 ? ((currentIndex + 1) / sessionCards.length) * 100 : 0;\n  const reviewedCount = reviewedCards.size;\n  const correctCount = correctCards.size;\n\n  const handleCardReview = (id: string, correct: boolean) => {\n    onReview(id, correct);\n    \n    setReviewedCards(prev => new Set([...prev, id]));\n    if (correct) {\n      setCorrectCards(prev => new Set([...prev, id]));\n    }\n\n    // 移动到下一张卡片或完成复习\n    if (currentIndex < sessionCards.length - 1) {\n      setCurrentIndex(currentIndex + 1);\n    } else {\n      // 复习完成\n      const timeSpent = Date.now() - startTime;\n      const results: ReviewResults = {\n        totalCards: sessionCards.length,\n        correctAnswers: correctCount + (correct ? 1 : 0),\n        incorrectAnswers: sessionCards.length - correctCount - (correct ? 1 : 0),\n        accuracy: ((correctCount + (correct ? 1 : 0)) / sessionCards.length) * 100,\n        timeSpent: Math.round(timeSpent / 1000), // 转换为秒\n      };\n      onComplete(results);\n    }\n  };\n\n  const handlePrevious = () => {\n    if (currentIndex > 0) {\n      setCurrentIndex(currentIndex - 1);\n    }\n  };\n\n  const handleNext = () => {\n    if (currentIndex < sessionCards.length - 1) {\n      setCurrentIndex(currentIndex + 1);\n    }\n  };\n\n  if (sessionCards.length === 0) {\n    return (\n      <Card className=\"w-full max-w-2xl mx-auto\">\n        <CardContent className=\"flex flex-col items-center justify-center py-12\">\n          <CheckCircle className=\"w-12 h-12 text-green-500 mb-4\" />\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">没有需要复习的卡片</h3>\n          <p className=\"text-gray-500 text-center mb-4\">\n            所有卡片都已经在最近复习过了！\n          </p>\n          <Button onClick={onExit}>返回</Button>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (!currentCard) return null;\n\n  return (\n    <div className=\"w-full max-w-4xl mx-auto space-y-6\">\n      {/* 复习进度 */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <CardTitle className=\"flex items-center gap-2\">\n              <Target className=\"w-5 h-5\" />\n              复习模式\n            </CardTitle>\n            <Button variant=\"outline\" onClick={onExit}>\n              退出复习\n            </Button>\n          </div>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center justify-between text-sm text-gray-600\">\n            <span>进度: {currentIndex + 1} / {sessionCards.length}</span>\n            <span>已复习: {reviewedCount}</span>\n            <span>正确: {correctCount}</span>\n          </div>\n          <Progress value={progress} className=\"w-full\" />\n        </CardContent>\n      </Card>\n\n      {/* 当前卡片 */}\n      <div className=\"flex justify-center\">\n        <FlashcardComponent\n          card={currentCard}\n          showActions={false}\n          onReview={handleCardReview}\n        />\n      </div>\n\n      {/* 导航按钮 */}\n      <div className=\"flex justify-center gap-4\">\n        <Button\n          variant=\"outline\"\n          onClick={handlePrevious}\n          disabled={currentIndex === 0}\n        >\n          上一张\n        </Button>\n        \n        <div className=\"flex items-center gap-2 px-4 py-2 bg-gray-100 rounded-lg\">\n          <Clock className=\"w-4 h-4 text-gray-500\" />\n          <span className=\"text-sm text-gray-600\">\n            {Math.round((Date.now() - startTime) / 1000)}s\n          </span>\n        </div>\n\n        <Button\n          variant=\"outline\"\n          onClick={handleNext}\n          disabled={currentIndex === sessionCards.length - 1}\n        >\n          下一张\n        </Button>\n      </div>\n\n      {/* 复习提示 */}\n      <Card className=\"bg-blue-50 border-blue-200\">\n        <CardContent className=\"p-4\">\n          <div className=\"flex items-start gap-3\">\n            <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\">\n              <RotateCcw className=\"w-4 h-4 text-blue-600\" />\n            </div>\n            <div className=\"space-y-1\">\n              <h4 className=\"font-medium text-blue-900\">复习提示</h4>\n              <p className=\"text-sm text-blue-700\">\n                仔细阅读问题，在心中思考答案，然后点击\"显示答案\"查看正确答案。\n                根据你的回答情况选择\"答对了\"或\"答错了\"。\n              </p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 卡片信息 */}\n      <Card>\n        <CardContent className=\"p-4\">\n          <div className=\"flex items-center justify-between text-sm text-gray-600\">\n            <div className=\"flex items-center gap-4\">\n              <Badge className={\n                currentCard.difficulty === 'easy' ? 'bg-green-100 text-green-800' :\n                currentCard.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :\n                'bg-red-100 text-red-800'\n              }>\n                {currentCard.difficulty === 'easy' ? '简单' :\n                 currentCard.difficulty === 'medium' ? '中等' : '困难'}\n              </Badge>\n              <span>复习次数: {currentCard.reviewCount}</span>\n              {currentCard.reviewCount > 0 && (\n                <span>\n                  历史正确率: {Math.round((currentCard.correctCount / currentCard.reviewCount) * 100)}%\n                </span>\n              )}\n            </div>\n            {currentCard.lastReviewed && (\n              <span>\n                上次复习: {currentCard.lastReviewed.toLocaleDateString()}\n              </span>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;AA0BO,SAAS,WAAW,KAAwD;QAAxD,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAmB,GAAxD;;IACzB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACpE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAClE,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,GAAG;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAEhE,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,MAAM,IAAI;YAChB,MAAM,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK;YAE1D,iCAAiC;YACjC,MAAM,cAAc;mBAAI;aAAM,CAAC,IAAI;oDAAC,CAAC,GAAG;oBACtC,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC,EAAE,YAAY,EAAE,OAAO;oBAC/C,IAAI,CAAC,EAAE,YAAY,EAAE,OAAO,CAAC;oBAC7B,IAAI,CAAC,EAAE,YAAY,EAAE,OAAO;oBAE5B,MAAM,YAAY,EAAE,WAAW,GAAG,IAAI,EAAE,YAAY,GAAG,EAAE,WAAW,GAAG;oBACvE,MAAM,YAAY,EAAE,WAAW,GAAG,IAAI,EAAE,YAAY,GAAG,EAAE,WAAW,GAAG;oBAEvE,UAAU;oBACV,IAAI,cAAc,WAAW,OAAO,YAAY;oBAEhD,aAAa;oBACb,OAAO,EAAE,YAAY,CAAC,OAAO,KAAK,EAAE,YAAY,CAAC,OAAO;gBAC1D;;YAEA,gBAAgB;QAClB;+BAAG;QAAC;KAAM;IAEV,MAAM,cAAc,YAAY,CAAC,aAAa;IAC9C,MAAM,WAAW,aAAa,MAAM,GAAG,IAAI,AAAC,CAAC,eAAe,CAAC,IAAI,aAAa,MAAM,GAAI,MAAM;IAC9F,MAAM,gBAAgB,cAAc,IAAI;IACxC,MAAM,eAAe,aAAa,IAAI;IAEtC,MAAM,mBAAmB,CAAC,IAAY;QACpC,SAAS,IAAI;QAEb,iBAAiB,CAAA,OAAQ,IAAI,IAAI;mBAAI;gBAAM;aAAG;QAC9C,IAAI,SAAS;YACX,gBAAgB,CAAA,OAAQ,IAAI,IAAI;uBAAI;oBAAM;iBAAG;QAC/C;QAEA,gBAAgB;QAChB,IAAI,eAAe,aAAa,MAAM,GAAG,GAAG;YAC1C,gBAAgB,eAAe;QACjC,OAAO;YACL,OAAO;YACP,MAAM,YAAY,KAAK,GAAG,KAAK;YAC/B,MAAM,UAAyB;gBAC7B,YAAY,aAAa,MAAM;gBAC/B,gBAAgB,eAAe,CAAC,UAAU,IAAI,CAAC;gBAC/C,kBAAkB,aAAa,MAAM,GAAG,eAAe,CAAC,UAAU,IAAI,CAAC;gBACvE,UAAU,AAAC,CAAC,eAAe,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI,aAAa,MAAM,GAAI;gBACvE,WAAW,KAAK,KAAK,CAAC,YAAY;YACpC;YACA,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,eAAe,GAAG;YACpB,gBAAgB,eAAe;QACjC;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,eAAe,aAAa,MAAM,GAAG,GAAG;YAC1C,gBAAgB,eAAe;QACjC;IACF;IAEA,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC,8NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAiC;;;;;;kCAG9C,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;kCAAQ;;;;;;;;;;;;;;;;;IAIjC;IAEA,IAAI,CAAC,aAAa,OAAO;IAEzB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGhC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAQ;;;;;;;;;;;;;;;;;kCAK/C,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAK;4CAAK,eAAe;4CAAE;4CAAI,aAAa,MAAM;;;;;;;kDACnD,6LAAC;;4CAAK;4CAAM;;;;;;;kDACZ,6LAAC;;4CAAK;4CAAK;;;;;;;;;;;;;0CAEb,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,OAAO;gCAAU,WAAU;;;;;;;;;;;;;;;;;;0BAKzC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,2IAAA,CAAA,qBAAkB;oBACjB,MAAM;oBACN,aAAa;oBACb,UAAU;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;wBACT,UAAU,iBAAiB;kCAC5B;;;;;;kCAID,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;oCAAM;;;;;;;;;;;;;kCAIjD,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;wBACT,UAAU,iBAAiB,aAAa,MAAM,GAAG;kCAClD;;;;;;;;;;;;0BAMH,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU7C,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WACL,YAAY,UAAU,KAAK,SAAS,gCACpC,YAAY,UAAU,KAAK,WAAW,kCACtC;kDAEC,YAAY,UAAU,KAAK,SAAS,OACpC,YAAY,UAAU,KAAK,WAAW,OAAO;;;;;;kDAEhD,6LAAC;;4CAAK;4CAAO,YAAY,WAAW;;;;;;;oCACnC,YAAY,WAAW,GAAG,mBACzB,6LAAC;;4CAAK;4CACI,KAAK,KAAK,CAAC,AAAC,YAAY,YAAY,GAAG,YAAY,WAAW,GAAI;4CAAK;;;;;;;;;;;;;4BAIpF,YAAY,YAAY,kBACvB,6LAAC;;oCAAK;oCACG,YAAY,YAAY,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlE;GAtMgB;KAAA", "debugId": null}}, {"offset": {"line": 3443, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/ReviewResults.tsx"], "sourcesContent": ["'use client';\n\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { CheckCircle, XCircle, Clock, Target, TrendingUp, RotateCcw } from 'lucide-react';\n\ninterface ReviewResults {\n  totalCards: number;\n  correctAnswers: number;\n  incorrectAnswers: number;\n  accuracy: number;\n  timeSpent: number;\n}\n\ninterface ReviewResultsProps {\n  results: ReviewResults;\n  onRestart: () => void;\n  onExit: () => void;\n}\n\nexport function ReviewResults({ results, onRestart, onExit }: ReviewResultsProps) {\n  const { totalCards, correctAnswers, incorrectAnswers, accuracy, timeSpent } = results;\n  \n  const formatTime = (seconds: number) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return minutes > 0 ? `${minutes}分${remainingSeconds}秒` : `${remainingSeconds}秒`;\n  };\n\n  const getAccuracyColor = (accuracy: number) => {\n    if (accuracy >= 80) return 'text-green-600 bg-green-50 border-green-200';\n    if (accuracy >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n    return 'text-red-600 bg-red-50 border-red-200';\n  };\n\n  const getAccuracyMessage = (accuracy: number) => {\n    if (accuracy >= 90) return '优秀！继续保持！';\n    if (accuracy >= 80) return '很好！再接再厉！';\n    if (accuracy >= 60) return '不错，还有提升空间';\n    return '需要加强复习哦';\n  };\n\n  const averageTimePerCard = totalCards > 0 ? Math.round(timeSpent / totalCards) : 0;\n\n  return (\n    <div className=\"w-full max-w-2xl mx-auto space-y-6\">\n      {/* 主要结果 */}\n      <Card className={`border-2 ${getAccuracyColor(accuracy)}`}>\n        <CardHeader className=\"text-center\">\n          <div className=\"flex justify-center mb-4\">\n            {accuracy >= 80 ? (\n              <CheckCircle className=\"w-16 h-16 text-green-500\" />\n            ) : (\n              <Target className=\"w-16 h-16 text-yellow-500\" />\n            )}\n          </div>\n          <CardTitle className=\"text-2xl\">复习完成！</CardTitle>\n          <p className=\"text-lg font-medium mt-2\">\n            {getAccuracyMessage(accuracy)}\n          </p>\n        </CardHeader>\n        \n        <CardContent className=\"space-y-6\">\n          {/* 准确率 */}\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold mb-2\">\n              {Math.round(accuracy)}%\n            </div>\n            <p className=\"text-gray-600\">准确率</p>\n          </div>\n\n          {/* 详细统计 */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n              <div className=\"text-2xl font-bold text-gray-900\">{totalCards}</div>\n              <p className=\"text-sm text-gray-600\">总卡片数</p>\n            </div>\n            \n            <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n              <div className=\"text-2xl font-bold text-gray-900\">{formatTime(timeSpent)}</div>\n              <p className=\"text-sm text-gray-600\">总用时</p>\n            </div>\n          </div>\n\n          {/* 正确/错误统计 */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"flex items-center justify-center p-4 bg-green-50 rounded-lg\">\n              <CheckCircle className=\"w-6 h-6 text-green-500 mr-2\" />\n              <div>\n                <div className=\"text-xl font-bold text-green-700\">{correctAnswers}</div>\n                <p className=\"text-sm text-green-600\">答对</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center justify-center p-4 bg-red-50 rounded-lg\">\n              <XCircle className=\"w-6 h-6 text-red-500 mr-2\" />\n              <div>\n                <div className=\"text-xl font-bold text-red-700\">{incorrectAnswers}</div>\n                <p className=\"text-sm text-red-600\">答错</p>\n              </div>\n            </div>\n          </div>\n\n          {/* 效率统计 */}\n          <div className=\"bg-blue-50 p-4 rounded-lg\">\n            <div className=\"flex items-center gap-2 mb-2\">\n              <Clock className=\"w-5 h-5 text-blue-600\" />\n              <span className=\"font-medium text-blue-900\">效率统计</span>\n            </div>\n            <div className=\"text-sm text-blue-700 space-y-1\">\n              <div>平均每张卡片用时: {averageTimePerCard}秒</div>\n              <div>\n                效率评价: {\n                  averageTimePerCard <= 10 ? '很快' :\n                  averageTimePerCard <= 20 ? '正常' :\n                  averageTimePerCard <= 30 ? '较慢' : '需要提高'\n                }\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 建议和鼓励 */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <TrendingUp className=\"w-5 h-5\" />\n            学习建议\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-3 text-sm\">\n            {accuracy >= 90 && (\n              <div className=\"flex items-start gap-2 p-3 bg-green-50 rounded-lg\">\n                <CheckCircle className=\"w-5 h-5 text-green-500 mt-0.5 flex-shrink-0\" />\n                <div>\n                  <p className=\"font-medium text-green-900\">表现优秀！</p>\n                  <p className=\"text-green-700\">你已经很好地掌握了这些知识点，可以尝试学习更高难度的内容。</p>\n                </div>\n              </div>\n            )}\n            \n            {accuracy >= 60 && accuracy < 90 && (\n              <div className=\"flex items-start gap-2 p-3 bg-yellow-50 rounded-lg\">\n                <Target className=\"w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0\" />\n                <div>\n                  <p className=\"font-medium text-yellow-900\">继续努力！</p>\n                  <p className=\"text-yellow-700\">建议重点复习答错的卡片，加深对知识点的理解。</p>\n                </div>\n              </div>\n            )}\n            \n            {accuracy < 60 && (\n              <div className=\"flex items-start gap-2 p-3 bg-red-50 rounded-lg\">\n                <XCircle className=\"w-5 h-5 text-red-500 mt-0.5 flex-shrink-0\" />\n                <div>\n                  <p className=\"font-medium text-red-900\">需要加强</p>\n                  <p className=\"text-red-700\">建议重新学习相关内容，然后再次进行复习练习。</p>\n                </div>\n              </div>\n            )}\n\n            <div className=\"border-t pt-3 mt-3\">\n              <p className=\"text-gray-600\">\n                💡 <strong>小贴士：</strong>定期复习是巩固记忆的关键。建议每天花10-15分钟复习之前学过的内容。\n              </p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 操作按钮 */}\n      <div className=\"flex gap-4\">\n        <Button\n          onClick={onRestart}\n          className=\"flex-1\"\n          variant=\"outline\"\n        >\n          <RotateCcw className=\"w-4 h-4 mr-2\" />\n          再次复习\n        </Button>\n        \n        <Button\n          onClick={onExit}\n          className=\"flex-1\"\n        >\n          返回主页\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAqBO,SAAS,cAAc,KAAkD;QAAlD,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAsB,GAAlD;IAC5B,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,gBAAgB,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAE9E,MAAM,aAAa,CAAC;QAClB,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QACrC,MAAM,mBAAmB,UAAU;QACnC,OAAO,UAAU,IAAI,AAAC,GAAa,OAAX,SAAQ,KAAoB,OAAjB,kBAAiB,OAAK,AAAC,GAAmB,OAAjB,kBAAiB;IAC/E;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,YAAY,IAAI,OAAO;QAC3B,IAAI,YAAY,IAAI,OAAO;QAC3B,OAAO;IACT;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,YAAY,IAAI,OAAO;QAC3B,IAAI,YAAY,IAAI,OAAO;QAC3B,IAAI,YAAY,IAAI,OAAO;QAC3B,OAAO;IACT;IAEA,MAAM,qBAAqB,aAAa,IAAI,KAAK,KAAK,CAAC,YAAY,cAAc;IAEjF,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAW,AAAC,YAAsC,OAA3B,iBAAiB;;kCAC5C,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC;gCAAI,WAAU;0CACZ,YAAY,mBACX,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAEvB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAGtB,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAW;;;;;;0CAChC,6LAAC;gCAAE,WAAU;0CACV,mBAAmB;;;;;;;;;;;;kCAIxB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,KAAK,CAAC;4CAAU;;;;;;;kDAExB,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAGvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoC,WAAW;;;;;;0DAC9D,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAKzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;kDAI1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAkC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEAAuB;;;;;;;;;;;;;;;;;;;;;;;;0CAM1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;kDAE9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAI;oDAAW;oDAAmB;;;;;;;0DACnC,6LAAC;;oDAAI;oDAED,sBAAsB,KAAK,OAC3B,sBAAsB,KAAK,OAC3B,sBAAsB,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9C,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAItC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,YAAY,oBACX,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAC1C,6LAAC;oDAAE,WAAU;8DAAiB;;;;;;;;;;;;;;;;;;gCAKnC,YAAY,MAAM,WAAW,oBAC5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA8B;;;;;;8DAC3C,6LAAC;oDAAE,WAAU;8DAAkB;;;;;;;;;;;;;;;;;;gCAKpC,WAAW,oBACV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA2B;;;;;;8DACxC,6LAAC;oDAAE,WAAU;8DAAe;;;;;;;;;;;;;;;;;;8CAKlC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;4CAAgB;0DACxB,6LAAC;0DAAO;;;;;;4CAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;wBACV,SAAQ;;0CAER,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAIxC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;KA5KgB", "debugId": null}}, {"offset": {"line": 4032, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAIoD;QAJpD,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D,GAJpD;IAKb,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAGI;QAHJ,EACxB,SAAS,EACT,GAAG,OACyB,GAHJ;IAIxB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 4110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,KAGoC;QAHpC,EACZ,SAAS,EACT,GAAG,OAC6C,GAHpC;IAIZ,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,KAGgC;QAHhC,EAChB,SAAS,EACT,GAAG,OAC6C,GAHhC;IAIhB,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 4190, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/lib/storage.ts"], "sourcesContent": ["import { Flashcard, Subject, ReviewSession, LearningStats } from '@/types';\n\n// 本地存储键名\nconst STORAGE_KEYS = {\n  FLASHCARDS: 'flashcards',\n  SUBJECTS: 'subjects',\n  REVIEW_SESSIONS: 'review_sessions',\n  LEARNING_STATS: 'learning_stats',\n} as const;\n\n// 生成唯一ID\nexport function generateId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n\n// 卡片存储操作\nexport class FlashcardStorage {\n  static getAll(): Flashcard[] {\n    if (typeof window === 'undefined') return [];\n    \n    const stored = localStorage.getItem(STORAGE_KEYS.FLASHCARDS);\n    if (!stored) return [];\n    \n    try {\n      const cards = JSON.parse(stored);\n      return cards.map((card: any) => ({\n        ...card,\n        createdAt: new Date(card.createdAt),\n        lastReviewed: card.lastReviewed ? new Date(card.lastReviewed) : undefined,\n      }));\n    } catch {\n      return [];\n    }\n  }\n\n  static save(cards: Flashcard[]): void {\n    if (typeof window === 'undefined') return;\n    localStorage.setItem(STORAGE_KEYS.FLASHCARDS, JSON.stringify(cards));\n  }\n\n  static add(card: Omit<Flashcard, 'id' | 'createdAt' | 'reviewCount' | 'correctCount'>): Flashcard {\n    const newCard: Flashcard = {\n      ...card,\n      id: generateId(),\n      createdAt: new Date(),\n      reviewCount: 0,\n      correctCount: 0,\n    };\n\n    const cards = this.getAll();\n    cards.push(newCard);\n    this.save(cards);\n    \n    return newCard;\n  }\n\n  static update(id: string, updates: Partial<Flashcard>): void {\n    const cards = this.getAll();\n    const index = cards.findIndex(card => card.id === id);\n    \n    if (index !== -1) {\n      cards[index] = { ...cards[index], ...updates };\n      this.save(cards);\n    }\n  }\n\n  static delete(id: string): void {\n    const cards = this.getAll();\n    const filtered = cards.filter(card => card.id !== id);\n    this.save(filtered);\n  }\n\n  static getBySubject(subject: string): Flashcard[] {\n    return this.getAll().filter(card => card.subject === subject);\n  }\n\n  static getDueForReview(): Flashcard[] {\n    const now = new Date();\n    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n    \n    return this.getAll().filter(card => \n      !card.lastReviewed || card.lastReviewed < oneDayAgo\n    );\n  }\n}\n\n// 主题存储操作\nexport class SubjectStorage {\n  static getAll(): Subject[] {\n    if (typeof window === 'undefined') return [];\n    \n    const stored = localStorage.getItem(STORAGE_KEYS.SUBJECTS);\n    if (!stored) return this.getDefaultSubjects();\n    \n    try {\n      return JSON.parse(stored);\n    } catch {\n      return this.getDefaultSubjects();\n    }\n  }\n\n  static save(subjects: Subject[]): void {\n    if (typeof window === 'undefined') return;\n    localStorage.setItem(STORAGE_KEYS.SUBJECTS, JSON.stringify(subjects));\n  }\n\n  static add(subject: Omit<Subject, 'id' | 'cardCount'>): Subject {\n    const newSubject: Subject = {\n      ...subject,\n      id: generateId(),\n      cardCount: 0,\n    };\n\n    const subjects = this.getAll();\n    subjects.push(newSubject);\n    this.save(subjects);\n    \n    return newSubject;\n  }\n\n  static updateCardCount(subjectName: string): void {\n    const subjects = this.getAll();\n    const cards = FlashcardStorage.getAll();\n    \n    subjects.forEach(subject => {\n      subject.cardCount = cards.filter(card => card.subject === subject.name).length;\n    });\n    \n    this.save(subjects);\n  }\n\n  private static getDefaultSubjects(): Subject[] {\n    return [\n      { id: '1', name: '语言学习', description: '外语词汇、语法等', color: '#3B82F6', cardCount: 0 },\n      { id: '2', name: '编程技术', description: '编程概念、算法等', color: '#10B981', cardCount: 0 },\n      { id: '3', name: '学术知识', description: '学科专业知识', color: '#8B5CF6', cardCount: 0 },\n      { id: '4', name: '生活常识', description: '日常生活知识', color: '#F59E0B', cardCount: 0 },\n      { id: '5', name: '其他', description: '其他类型知识', color: '#6B7280', cardCount: 0 },\n    ];\n  }\n}\n\n// 学习统计\nexport class StatsStorage {\n  static get(): LearningStats {\n    if (typeof window === 'undefined') {\n      return this.getDefaultStats();\n    }\n    \n    const stored = localStorage.getItem(STORAGE_KEYS.LEARNING_STATS);\n    if (!stored) return this.getDefaultStats();\n    \n    try {\n      const stats = JSON.parse(stored);\n      // 转换日期字符串为Date对象\n      Object.keys(stats.subjectStats).forEach(key => {\n        if (stats.subjectStats[key].lastReviewed) {\n          stats.subjectStats[key].lastReviewed = new Date(stats.subjectStats[key].lastReviewed);\n        }\n      });\n      return stats;\n    } catch {\n      return this.getDefaultStats();\n    }\n  }\n\n  static save(stats: LearningStats): void {\n    if (typeof window === 'undefined') return;\n    localStorage.setItem(STORAGE_KEYS.LEARNING_STATS, JSON.stringify(stats));\n  }\n\n  static updateAfterReview(subjectName: string, correct: boolean): void {\n    const stats = this.get();\n    \n    stats.totalReviews++;\n    if (correct) {\n      stats.totalCards++;\n    }\n    \n    // 更新主题统计\n    if (!stats.subjectStats[subjectName]) {\n      stats.subjectStats[subjectName] = {\n        cardCount: 0,\n        accuracy: 0,\n        lastReviewed: new Date(),\n      };\n    }\n    \n    const subjectStat = stats.subjectStats[subjectName];\n    subjectStat.lastReviewed = new Date();\n    \n    // 简单的准确率计算\n    const oldAccuracy = subjectStat.accuracy;\n    const reviewCount = subjectStat.cardCount + 1;\n    subjectStat.accuracy = (oldAccuracy * subjectStat.cardCount + (correct ? 1 : 0)) / reviewCount;\n    subjectStat.cardCount = reviewCount;\n    \n    // 重新计算总体准确率\n    const totalCorrect = Object.values(stats.subjectStats).reduce(\n      (sum, stat) => sum + (stat.accuracy * stat.cardCount), 0\n    );\n    stats.averageAccuracy = totalCorrect / stats.totalReviews;\n    \n    this.save(stats);\n  }\n\n  private static getDefaultStats(): LearningStats {\n    return {\n      totalCards: 0,\n      totalReviews: 0,\n      averageAccuracy: 0,\n      streakDays: 0,\n      subjectStats: {},\n    };\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAEA,SAAS;AACT,MAAM,eAAe;IACnB,YAAY;IACZ,UAAU;IACV,iBAAiB;IACjB,gBAAgB;AAClB;AAGO,SAAS;IACd,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE;AAGO,MAAM;IACX,OAAO,SAAsB;QAC3B;;QAEA,MAAM,SAAS,aAAa,OAAO,CAAC,aAAa,UAAU;QAC3D,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,IAAI;YACF,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,OAAO,MAAM,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC/B,GAAG,IAAI;oBACP,WAAW,IAAI,KAAK,KAAK,SAAS;oBAClC,cAAc,KAAK,YAAY,GAAG,IAAI,KAAK,KAAK,YAAY,IAAI;gBAClE,CAAC;QACH,EAAE,UAAM;YACN,OAAO,EAAE;QACX;IACF;IAEA,OAAO,KAAK,KAAkB,EAAQ;QACpC;;QACA,aAAa,OAAO,CAAC,aAAa,UAAU,EAAE,KAAK,SAAS,CAAC;IAC/D;IAEA,OAAO,IAAI,IAA0E,EAAa;QAChG,MAAM,UAAqB;YACzB,GAAG,IAAI;YACP,IAAI;YACJ,WAAW,IAAI;YACf,aAAa;YACb,cAAc;QAChB;QAEA,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,IAAI,CAAC;QACX,IAAI,CAAC,IAAI,CAAC;QAEV,OAAO;IACT;IAEA,OAAO,OAAO,EAAU,EAAE,OAA2B,EAAQ;QAC3D,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAElD,IAAI,UAAU,CAAC,GAAG;YAChB,KAAK,CAAC,MAAM,GAAG;gBAAE,GAAG,KAAK,CAAC,MAAM;gBAAE,GAAG,OAAO;YAAC;YAC7C,IAAI,CAAC,IAAI,CAAC;QACZ;IACF;IAEA,OAAO,OAAO,EAAU,EAAQ;QAC9B,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,CAAC,IAAI,CAAC;IACZ;IAEA,OAAO,aAAa,OAAe,EAAe;QAChD,OAAO,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK;IACvD;IAEA,OAAO,kBAA+B;QACpC,MAAM,MAAM,IAAI;QAChB,MAAM,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK;QAE1D,OAAO,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAA,OAC1B,CAAC,KAAK,YAAY,IAAI,KAAK,YAAY,GAAG;IAE9C;AACF;AAGO,MAAM;IACX,OAAO,SAAoB;QACzB;;QAEA,MAAM,SAAS,aAAa,OAAO,CAAC,aAAa,QAAQ;QACzD,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,kBAAkB;QAE3C,IAAI;YACF,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,UAAM;YACN,OAAO,IAAI,CAAC,kBAAkB;QAChC;IACF;IAEA,OAAO,KAAK,QAAmB,EAAQ;QACrC;;QACA,aAAa,OAAO,CAAC,aAAa,QAAQ,EAAE,KAAK,SAAS,CAAC;IAC7D;IAEA,OAAO,IAAI,OAA0C,EAAW;QAC9D,MAAM,aAAsB;YAC1B,GAAG,OAAO;YACV,IAAI;YACJ,WAAW;QACb;QAEA,MAAM,WAAW,IAAI,CAAC,MAAM;QAC5B,SAAS,IAAI,CAAC;QACd,IAAI,CAAC,IAAI,CAAC;QAEV,OAAO;IACT;IAEA,OAAO,gBAAgB,WAAmB,EAAQ;QAChD,MAAM,WAAW,IAAI,CAAC,MAAM;QAC5B,MAAM,QAAQ,iBAAiB,MAAM;QAErC,SAAS,OAAO,CAAC,CAAA;YACf,QAAQ,SAAS,GAAG,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK,QAAQ,IAAI,EAAE,MAAM;QAChF;QAEA,IAAI,CAAC,IAAI,CAAC;IACZ;IAEA,OAAe,qBAAgC;QAC7C,OAAO;YACL;gBAAE,IAAI;gBAAK,MAAM;gBAAQ,aAAa;gBAAY,OAAO;gBAAW,WAAW;YAAE;YACjF;gBAAE,IAAI;gBAAK,MAAM;gBAAQ,aAAa;gBAAY,OAAO;gBAAW,WAAW;YAAE;YACjF;gBAAE,IAAI;gBAAK,MAAM;gBAAQ,aAAa;gBAAU,OAAO;gBAAW,WAAW;YAAE;YAC/E;gBAAE,IAAI;gBAAK,MAAM;gBAAQ,aAAa;gBAAU,OAAO;gBAAW,WAAW;YAAE;YAC/E;gBAAE,IAAI;gBAAK,MAAM;gBAAM,aAAa;gBAAU,OAAO;gBAAW,WAAW;YAAE;SAC9E;IACH;AACF;AAGO,MAAM;IACX,OAAO,MAAqB;QAC1B;;QAIA,MAAM,SAAS,aAAa,OAAO,CAAC,aAAa,cAAc;QAC/D,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,eAAe;QAExC,IAAI;YACF,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,iBAAiB;YACjB,OAAO,IAAI,CAAC,MAAM,YAAY,EAAE,OAAO,CAAC,CAAA;gBACtC,IAAI,MAAM,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE;oBACxC,MAAM,YAAY,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,KAAK,MAAM,YAAY,CAAC,IAAI,CAAC,YAAY;gBACtF;YACF;YACA,OAAO;QACT,EAAE,UAAM;YACN,OAAO,IAAI,CAAC,eAAe;QAC7B;IACF;IAEA,OAAO,KAAK,KAAoB,EAAQ;QACtC;;QACA,aAAa,OAAO,CAAC,aAAa,cAAc,EAAE,KAAK,SAAS,CAAC;IACnE;IAEA,OAAO,kBAAkB,WAAmB,EAAE,OAAgB,EAAQ;QACpE,MAAM,QAAQ,IAAI,CAAC,GAAG;QAEtB,MAAM,YAAY;QAClB,IAAI,SAAS;YACX,MAAM,UAAU;QAClB;QAEA,SAAS;QACT,IAAI,CAAC,MAAM,YAAY,CAAC,YAAY,EAAE;YACpC,MAAM,YAAY,CAAC,YAAY,GAAG;gBAChC,WAAW;gBACX,UAAU;gBACV,cAAc,IAAI;YACpB;QACF;QAEA,MAAM,cAAc,MAAM,YAAY,CAAC,YAAY;QACnD,YAAY,YAAY,GAAG,IAAI;QAE/B,WAAW;QACX,MAAM,cAAc,YAAY,QAAQ;QACxC,MAAM,cAAc,YAAY,SAAS,GAAG;QAC5C,YAAY,QAAQ,GAAG,CAAC,cAAc,YAAY,SAAS,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC,IAAI;QACnF,YAAY,SAAS,GAAG;QAExB,YAAY;QACZ,MAAM,eAAe,OAAO,MAAM,CAAC,MAAM,YAAY,EAAE,MAAM,CAC3D,CAAC,KAAK,OAAS,MAAO,KAAK,QAAQ,GAAG,KAAK,SAAS,EAAG;QAEzD,MAAM,eAAe,GAAG,eAAe,MAAM,YAAY;QAEzD,IAAI,CAAC,IAAI,CAAC;IACZ;IAEA,OAAe,kBAAiC;QAC9C,OAAO;YACL,YAAY;YACZ,cAAc;YACd,iBAAiB;YACjB,YAAY;YACZ,cAAc,CAAC;QACjB;IACF;AACF", "debugId": null}}, {"offset": {"line": 4411, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/project/AIProject/Duix.Heygem/ai-flashcard-generator/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { TextInputForm } from '@/components/TextInputForm';\nimport { FlashcardList } from '@/components/FlashcardList';\nimport { EditCardDialog } from '@/components/EditCardDialog';\nimport { ReviewMode } from '@/components/ReviewMode';\nimport { ReviewResults } from '@/components/ReviewResults';\nimport { Button } from '@/components/ui/button';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { FlashcardStorage, SubjectStorage, StatsStorage } from '@/lib/storage';\nimport { GenerateCardsRequest, Flashcard } from '@/types';\nimport { Brain, Plus, Library, BarChart3, Play } from 'lucide-react';\n\nexport default function Home() {\n  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);\n  const [subjects, setSubjects] = useState<string[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState('create');\n  const [editingCard, setEditingCard] = useState<Flashcard | null>(null);\n  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);\n  const [isReviewMode, setIsReviewMode] = useState(false);\n  const [reviewResults, setReviewResults] = useState<any>(null);\n\n  // 加载数据\n  useEffect(() => {\n    const loadedCards = FlashcardStorage.getAll();\n    const loadedSubjects = SubjectStorage.getAll();\n\n    setFlashcards(loadedCards);\n    setSubjects(loadedSubjects.map(s => s.name));\n  }, []);\n\n  // 生成卡片\n  const handleGenerateCards = async (request: GenerateCardsRequest) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/generate', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(request),\n      });\n\n      const result = await response.json();\n\n      if (!result.success) {\n        throw new Error(result.error || '生成卡片失败');\n      }\n\n      // 保存生成的卡片\n      const newCards: Flashcard[] = [];\n      result.cards.forEach((cardData: any) => {\n        const newCard = FlashcardStorage.add({\n          ...cardData,\n          source: request.text.substring(0, 100) + '...',\n        });\n        newCards.push(newCard);\n      });\n\n      // 更新状态\n      setFlashcards(prev => [...newCards, ...prev]);\n\n      // 更新主题卡片数量\n      SubjectStorage.updateCardCount(request.subject);\n\n      // 切换到卡片列表标签\n      setActiveTab('library');\n\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '生成卡片时发生未知错误');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 编辑卡片\n  const handleEditCard = (card: Flashcard) => {\n    setEditingCard(card);\n    setIsEditDialogOpen(true);\n  };\n\n  // 保存编辑的卡片\n  const handleSaveEditedCard = (updatedCard: Flashcard) => {\n    FlashcardStorage.update(updatedCard.id, updatedCard);\n    setFlashcards(prev => prev.map(card =>\n      card.id === updatedCard.id ? updatedCard : card\n    ));\n\n    // 如果主题改变了，更新主题卡片数量\n    const originalCard = flashcards.find(c => c.id === updatedCard.id);\n    if (originalCard && originalCard.subject !== updatedCard.subject) {\n      SubjectStorage.updateCardCount(originalCard.subject);\n      SubjectStorage.updateCardCount(updatedCard.subject);\n    }\n  };\n\n  // 删除卡片\n  const handleDeleteCard = (id: string) => {\n    if (confirm('确定要删除这张卡片吗？')) {\n      FlashcardStorage.delete(id);\n      setFlashcards(prev => prev.filter(card => card.id !== id));\n\n      // 更新主题卡片数量\n      const card = flashcards.find(c => c.id === id);\n      if (card) {\n        SubjectStorage.updateCardCount(card.subject);\n      }\n    }\n  };\n\n  // 复习卡片\n  const handleReviewCard = (id: string, correct: boolean) => {\n    const card = flashcards.find(c => c.id === id);\n    if (!card) return;\n\n    // 更新卡片统计\n    FlashcardStorage.update(id, {\n      reviewCount: card.reviewCount + 1,\n      correctCount: card.correctCount + (correct ? 1 : 0),\n      lastReviewed: new Date(),\n    });\n\n    // 更新学习统计\n    StatsStorage.updateAfterReview(card.subject, correct);\n\n    // 更新本地状态\n    setFlashcards(prev => prev.map(c =>\n      c.id === id\n        ? {\n            ...c,\n            reviewCount: c.reviewCount + 1,\n            correctCount: c.correctCount + (correct ? 1 : 0),\n            lastReviewed: new Date(),\n          }\n        : c\n    ));\n  };\n\n  // 开始复习模式\n  const handleStartReview = () => {\n    if (flashcards.length === 0) {\n      setError('没有可复习的卡片');\n      return;\n    }\n    setIsReviewMode(true);\n    setReviewResults(null);\n  };\n\n  // 复习完成\n  const handleReviewComplete = (results: any) => {\n    setReviewResults(results);\n    setIsReviewMode(false);\n  };\n\n  // 重新开始复习\n  const handleRestartReview = () => {\n    setReviewResults(null);\n    setIsReviewMode(true);\n  };\n\n  // 退出复习\n  const handleExitReview = () => {\n    setIsReviewMode(false);\n    setReviewResults(null);\n    setActiveTab('library');\n  };\n\n  // 如果在复习模式或显示复习结果，显示不同的界面\n  if (isReviewMode) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <header className=\"bg-white shadow-sm border-b\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex items-center justify-between h-16\">\n              <div className=\"flex items-center gap-3\">\n                <Brain className=\"w-8 h-8 text-blue-600\" />\n                <h1 className=\"text-xl font-bold text-gray-900\">AI学习卡片生成器 - 复习模式</h1>\n              </div>\n            </div>\n          </div>\n        </header>\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <ReviewMode\n            cards={flashcards}\n            onReview={handleReviewCard}\n            onComplete={handleReviewComplete}\n            onExit={handleExitReview}\n          />\n        </main>\n      </div>\n    );\n  }\n\n  if (reviewResults) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <header className=\"bg-white shadow-sm border-b\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex items-center justify-between h-16\">\n              <div className=\"flex items-center gap-3\">\n                <Brain className=\"w-8 h-8 text-blue-600\" />\n                <h1 className=\"text-xl font-bold text-gray-900\">AI学习卡片生成器 - 复习结果</h1>\n              </div>\n            </div>\n          </div>\n        </header>\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <ReviewResults\n            results={reviewResults}\n            onRestart={handleRestartReview}\n            onExit={handleExitReview}\n          />\n        </main>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 头部 */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16\">\n            <div className=\"flex items-center gap-3\">\n              <Brain className=\"w-8 h-8 text-blue-600\" />\n              <h1 className=\"text-xl font-bold text-gray-900\">AI学习卡片生成器</h1>\n            </div>\n            <div className=\"flex items-center gap-4\">\n              {flashcards.length > 0 && (\n                <Button onClick={handleStartReview} className=\"flex items-center gap-2\">\n                  <Play className=\"w-4 h-4\" />\n                  开始复习\n                </Button>\n              )}\n              <div className=\"text-sm text-gray-500\">\n                基于 Google Gemini 2.0 Flash\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* 主要内容 */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {error && (\n          <Alert className=\"mb-6 border-red-200 bg-red-50\">\n            <AlertDescription className=\"text-red-800\">\n              {error}\n            </AlertDescription>\n          </Alert>\n        )}\n\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"space-y-6\">\n          <TabsList className=\"grid w-full grid-cols-3\">\n            <TabsTrigger value=\"create\" className=\"flex items-center gap-2\">\n              <Plus className=\"w-4 h-4\" />\n              创建卡片\n            </TabsTrigger>\n            <TabsTrigger value=\"library\" className=\"flex items-center gap-2\">\n              <Library className=\"w-4 h-4\" />\n              卡片库 ({flashcards.length})\n            </TabsTrigger>\n            <TabsTrigger value=\"stats\" className=\"flex items-center gap-2\">\n              <BarChart3 className=\"w-4 h-4\" />\n              学习统计\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"create\" className=\"space-y-6\">\n            <TextInputForm\n              onGenerate={handleGenerateCards}\n              isLoading={isLoading}\n              subjects={subjects}\n            />\n          </TabsContent>\n\n          <TabsContent value=\"library\" className=\"space-y-6\">\n            <FlashcardList\n              cards={flashcards}\n              onEdit={handleEditCard}\n              onDelete={handleDeleteCard}\n              onReview={handleReviewCard}\n            />\n          </TabsContent>\n\n          <TabsContent value=\"stats\" className=\"space-y-6\">\n            <div className=\"text-center py-12\">\n              <BarChart3 className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">学习统计</h3>\n              <p className=\"text-gray-500\">统计功能正在开发中...</p>\n            </div>\n          </TabsContent>\n        </Tabs>\n      </main>\n\n      {/* 编辑卡片对话框 */}\n      <EditCardDialog\n        card={editingCard}\n        isOpen={isEditDialogOpen}\n        onClose={() => {\n          setIsEditDialogOpen(false);\n          setEditingCard(null);\n        }}\n        onSave={handleSaveEditedCard}\n        subjects={subjects}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;;;;;;AAee,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAExD,OAAO;IACP,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,cAAc,wHAAA,CAAA,mBAAgB,CAAC,MAAM;YAC3C,MAAM,iBAAiB,wHAAA,CAAA,iBAAc,CAAC,MAAM;YAE5C,cAAc;YACd,YAAY,eAAe,GAAG;kCAAC,CAAA,IAAK,EAAE,IAAI;;QAC5C;yBAAG,EAAE;IAEL,OAAO;IACP,MAAM,sBAAsB,OAAO;QACjC,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,UAAU;YACV,MAAM,WAAwB,EAAE;YAChC,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC;gBACpB,MAAM,UAAU,wHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC;oBACnC,GAAG,QAAQ;oBACX,QAAQ,QAAQ,IAAI,CAAC,SAAS,CAAC,GAAG,OAAO;gBAC3C;gBACA,SAAS,IAAI,CAAC;YAChB;YAEA,OAAO;YACP,cAAc,CAAA,OAAQ;uBAAI;uBAAa;iBAAK;YAE5C,WAAW;YACX,wHAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,QAAQ,OAAO;YAE9C,YAAY;YACZ,aAAa;QAEf,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,OAAO;IACP,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,oBAAoB;IACtB;IAEA,UAAU;IACV,MAAM,uBAAuB,CAAC;QAC5B,wHAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE;QACxC,cAAc,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OAC7B,KAAK,EAAE,KAAK,YAAY,EAAE,GAAG,cAAc;QAG7C,mBAAmB;QACnB,MAAM,eAAe,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,EAAE;QACjE,IAAI,gBAAgB,aAAa,OAAO,KAAK,YAAY,OAAO,EAAE;YAChE,wHAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,aAAa,OAAO;YACnD,wHAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,YAAY,OAAO;QACpD;IACF;IAEA,OAAO;IACP,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ,gBAAgB;YAC1B,wHAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC;YACxB,cAAc,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAEtD,WAAW;YACX,MAAM,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC3C,IAAI,MAAM;gBACR,wHAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,KAAK,OAAO;YAC7C;QACF;IACF;IAEA,OAAO;IACP,MAAM,mBAAmB,CAAC,IAAY;QACpC,MAAM,OAAO,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC3C,IAAI,CAAC,MAAM;QAEX,SAAS;QACT,wHAAA,CAAA,mBAAgB,CAAC,MAAM,CAAC,IAAI;YAC1B,aAAa,KAAK,WAAW,GAAG;YAChC,cAAc,KAAK,YAAY,GAAG,CAAC,UAAU,IAAI,CAAC;YAClD,cAAc,IAAI;QACpB;QAEA,SAAS;QACT,wHAAA,CAAA,eAAY,CAAC,iBAAiB,CAAC,KAAK,OAAO,EAAE;QAE7C,SAAS;QACT,cAAc,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAC7B,EAAE,EAAE,KAAK,KACL;oBACE,GAAG,CAAC;oBACJ,aAAa,EAAE,WAAW,GAAG;oBAC7B,cAAc,EAAE,YAAY,GAAG,CAAC,UAAU,IAAI,CAAC;oBAC/C,cAAc,IAAI;gBACpB,IACA;IAER;IAEA,SAAS;IACT,MAAM,oBAAoB;QACxB,IAAI,WAAW,MAAM,KAAK,GAAG;YAC3B,SAAS;YACT;QACF;QACA,gBAAgB;QAChB,iBAAiB;IACnB;IAEA,OAAO;IACP,MAAM,uBAAuB,CAAC;QAC5B,iBAAiB;QACjB,gBAAgB;IAClB;IAEA,SAAS;IACT,MAAM,sBAAsB;QAC1B,iBAAiB;QACjB,gBAAgB;IAClB;IAEA,OAAO;IACP,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,iBAAiB;QACjB,aAAa;IACf;IAEA,yBAAyB;IACzB,IAAI,cAAc;QAChB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKxD,6LAAC;oBAAK,WAAU;8BACd,cAAA,6LAAC,mIAAA,CAAA,aAAU;wBACT,OAAO;wBACP,UAAU;wBACV,YAAY;wBACZ,QAAQ;;;;;;;;;;;;;;;;;IAKlB;IAEA,IAAI,eAAe;QACjB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAKxD,6LAAC;oBAAK,WAAU;8BACd,cAAA,6LAAC,sIAAA,CAAA,gBAAa;wBACZ,SAAS;wBACT,WAAW;wBACX,QAAQ;;;;;;;;;;;;;;;;;IAKlB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;;;;;;;0CAElD,6LAAC;gCAAI,WAAU;;oCACZ,WAAW,MAAM,GAAG,mBACnB,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAmB,WAAU;;0DAC5C,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAIhC,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/C,6LAAC;gBAAK,WAAU;;oBACb,uBACC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,WAAU;kCACf,cAAA,6LAAC,oIAAA,CAAA,mBAAgB;4BAAC,WAAU;sCACzB;;;;;;;;;;;kCAKP,6LAAC,mIAAA,CAAA,OAAI;wBAAC,OAAO;wBAAW,eAAe;wBAAc,WAAU;;0CAC7D,6LAAC,mIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAS,WAAU;;0DACpC,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG9B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAU,WAAU;;0DACrC,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAY;4CACzB,WAAW,MAAM;4CAAC;;;;;;;kDAE1B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAQ,WAAU;;0DACnC,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;0CAKrC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAS,WAAU;0CACpC,cAAA,6LAAC,sIAAA,CAAA,gBAAa;oCACZ,YAAY;oCACZ,WAAW;oCACX,UAAU;;;;;;;;;;;0CAId,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAU,WAAU;0CACrC,cAAA,6LAAC,sIAAA,CAAA,gBAAa;oCACZ,OAAO;oCACP,QAAQ;oCACR,UAAU;oCACV,UAAU;;;;;;;;;;;0CAId,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAQ,WAAU;0CACnC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,6LAAC,uIAAA,CAAA,iBAAc;gBACb,MAAM;gBACN,QAAQ;gBACR,SAAS;oBACP,oBAAoB;oBACpB,eAAe;gBACjB;gBACA,QAAQ;gBACR,UAAU;;;;;;;;;;;;AAIlB;GA3SwB;KAAA", "debugId": null}}]}