// 学习卡片数据模型
export interface Flashcard {
  id: string;
  question: string;
  answer: string;
  difficulty: 'easy' | 'medium' | 'hard';
  subject: string;
  tags: string[];
  createdAt: Date;
  lastReviewed?: Date;
  reviewCount: number;
  correctCount: number;
  source?: string; // 原始文本来源
}

// 学习主题
export interface Subject {
  id: string;
  name: string;
  description?: string;
  color: string;
  cardCount: number;
}

// 复习会话
export interface ReviewSession {
  id: string;
  subjectId: string;
  startTime: Date;
  endTime?: Date;
  totalCards: number;
  correctAnswers: number;
  completedCards: Flashcard[];
}

// API请求/响应类型
export interface GenerateCardsRequest {
  text: string;
  subject: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  cardCount?: number;
}

export interface GenerateCardsResponse {
  cards: Omit<Flashcard, 'id' | 'createdAt' | 'reviewCount' | 'correctCount'>[];
  success: boolean;
  error?: string;
}

// 用户学习统计
export interface LearningStats {
  totalCards: number;
  totalReviews: number;
  averageAccuracy: number;
  streakDays: number;
  subjectStats: {
    [subjectId: string]: {
      cardCount: number;
      accuracy: number;
      lastReviewed: Date;
    };
  };
}

// 文件上传类型
export interface FileUpload {
  file: File;
  type: 'text' | 'pdf' | 'docx';
  content?: string;
}
