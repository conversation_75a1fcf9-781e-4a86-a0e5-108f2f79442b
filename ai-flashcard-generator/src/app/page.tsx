'use client';

import { useState, useEffect } from 'react';
import { TextInputForm } from '@/components/TextInputForm';
import { FlashcardList } from '@/components/FlashcardList';
import { EditCardDialog } from '@/components/EditCardDialog';
import { ReviewMode } from '@/components/ReviewMode';
import { ReviewResults } from '@/components/ReviewResults';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { FlashcardStorage, SubjectStorage, StatsStorage } from '@/lib/storage';
import { GenerateCardsRequest, Flashcard } from '@/types';
import { Brain, Plus, Library, BarChart3, Play } from 'lucide-react';

export default function Home() {
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [subjects, setSubjects] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('create');
  const [editingCard, setEditingCard] = useState<Flashcard | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isReviewMode, setIsReviewMode] = useState(false);
  const [reviewResults, setReviewResults] = useState<any>(null);

  // 加载数据
  useEffect(() => {
    const loadedCards = FlashcardStorage.getAll();
    const loadedSubjects = SubjectStorage.getAll();

    setFlashcards(loadedCards);
    setSubjects(loadedSubjects.map(s => s.name));
  }, []);

  // 生成卡片
  const handleGenerateCards = async (request: GenerateCardsRequest) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || '生成卡片失败');
      }

      // 保存生成的卡片
      const newCards: Flashcard[] = [];
      result.cards.forEach((cardData: any) => {
        const newCard = FlashcardStorage.add({
          ...cardData,
          source: request.text.substring(0, 100) + '...',
        });
        newCards.push(newCard);
      });

      // 更新状态
      setFlashcards(prev => [...newCards, ...prev]);

      // 更新主题卡片数量
      SubjectStorage.updateCardCount(request.subject);

      // 切换到卡片列表标签
      setActiveTab('library');

    } catch (err) {
      setError(err instanceof Error ? err.message : '生成卡片时发生未知错误');
    } finally {
      setIsLoading(false);
    }
  };

  // 编辑卡片
  const handleEditCard = (card: Flashcard) => {
    setEditingCard(card);
    setIsEditDialogOpen(true);
  };

  // 保存编辑的卡片
  const handleSaveEditedCard = (updatedCard: Flashcard) => {
    FlashcardStorage.update(updatedCard.id, updatedCard);
    setFlashcards(prev => prev.map(card =>
      card.id === updatedCard.id ? updatedCard : card
    ));

    // 如果主题改变了，更新主题卡片数量
    const originalCard = flashcards.find(c => c.id === updatedCard.id);
    if (originalCard && originalCard.subject !== updatedCard.subject) {
      SubjectStorage.updateCardCount(originalCard.subject);
      SubjectStorage.updateCardCount(updatedCard.subject);
    }
  };

  // 删除卡片
  const handleDeleteCard = (id: string) => {
    if (confirm('确定要删除这张卡片吗？')) {
      FlashcardStorage.delete(id);
      setFlashcards(prev => prev.filter(card => card.id !== id));

      // 更新主题卡片数量
      const card = flashcards.find(c => c.id === id);
      if (card) {
        SubjectStorage.updateCardCount(card.subject);
      }
    }
  };

  // 复习卡片
  const handleReviewCard = (id: string, correct: boolean) => {
    const card = flashcards.find(c => c.id === id);
    if (!card) return;

    // 更新卡片统计
    FlashcardStorage.update(id, {
      reviewCount: card.reviewCount + 1,
      correctCount: card.correctCount + (correct ? 1 : 0),
      lastReviewed: new Date(),
    });

    // 更新学习统计
    StatsStorage.updateAfterReview(card.subject, correct);

    // 更新本地状态
    setFlashcards(prev => prev.map(c =>
      c.id === id
        ? {
            ...c,
            reviewCount: c.reviewCount + 1,
            correctCount: c.correctCount + (correct ? 1 : 0),
            lastReviewed: new Date(),
          }
        : c
    ));
  };

  // 开始复习模式
  const handleStartReview = () => {
    if (flashcards.length === 0) {
      setError('没有可复习的卡片');
      return;
    }
    setIsReviewMode(true);
    setReviewResults(null);
  };

  // 复习完成
  const handleReviewComplete = (results: any) => {
    setReviewResults(results);
    setIsReviewMode(false);
  };

  // 重新开始复习
  const handleRestartReview = () => {
    setReviewResults(null);
    setIsReviewMode(true);
  };

  // 退出复习
  const handleExitReview = () => {
    setIsReviewMode(false);
    setReviewResults(null);
    setActiveTab('library');
  };

  // 如果在复习模式或显示复习结果，显示不同的界面
  if (isReviewMode) {
    return (
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center gap-3">
                <Brain className="w-8 h-8 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-900">AI学习卡片生成器 - 复习模式</h1>
              </div>
            </div>
          </div>
        </header>
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <ReviewMode
            cards={flashcards}
            onReview={handleReviewCard}
            onComplete={handleReviewComplete}
            onExit={handleExitReview}
          />
        </main>
      </div>
    );
  }

  if (reviewResults) {
    return (
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center gap-3">
                <Brain className="w-8 h-8 text-blue-600" />
                <h1 className="text-xl font-bold text-gray-900">AI学习卡片生成器 - 复习结果</h1>
              </div>
            </div>
          </div>
        </header>
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <ReviewResults
            results={reviewResults}
            onRestart={handleRestartReview}
            onExit={handleExitReview}
          />
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <Brain className="w-8 h-8 text-blue-600" />
              <h1 className="text-xl font-bold text-gray-900">AI学习卡片生成器</h1>
            </div>
            <div className="flex items-center gap-4">
              {flashcards.length > 0 && (
                <Button onClick={handleStartReview} className="flex items-center gap-2">
                  <Play className="w-4 h-4" />
                  开始复习
                </Button>
              )}
              <div className="text-sm text-gray-500">
                基于 Google Gemini 2.0 Flash
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="create" className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              创建卡片
            </TabsTrigger>
            <TabsTrigger value="library" className="flex items-center gap-2">
              <Library className="w-4 h-4" />
              卡片库 ({flashcards.length})
            </TabsTrigger>
            <TabsTrigger value="stats" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              学习统计
            </TabsTrigger>
          </TabsList>

          <TabsContent value="create" className="space-y-6">
            <TextInputForm
              onGenerate={handleGenerateCards}
              isLoading={isLoading}
              subjects={subjects}
            />
          </TabsContent>

          <TabsContent value="library" className="space-y-6">
            <FlashcardList
              cards={flashcards}
              onEdit={handleEditCard}
              onDelete={handleDeleteCard}
              onReview={handleReviewCard}
            />
          </TabsContent>

          <TabsContent value="stats" className="space-y-6">
            <div className="text-center py-12">
              <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">学习统计</h3>
              <p className="text-gray-500">统计功能正在开发中...</p>
            </div>
          </TabsContent>
        </Tabs>
      </main>

      {/* 编辑卡片对话框 */}
      <EditCardDialog
        card={editingCard}
        isOpen={isEditDialogOpen}
        onClose={() => {
          setIsEditDialogOpen(false);
          setEditingCard(null);
        }}
        onSave={handleSaveEditedCard}
        subjects={subjects}
      />
    </div>
  );
}
