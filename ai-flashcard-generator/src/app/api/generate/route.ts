import { NextRequest, NextResponse } from 'next/server';
import { generateFlashcards } from '@/lib/gemini';
import { GenerateCardsRequest } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body: GenerateCardsRequest = await request.json();
    
    // 验证请求数据
    if (!body.text || !body.subject) {
      return NextResponse.json(
        { success: false, error: 'Text and subject are required' },
        { status: 400 }
      );
    }

    // 检查API密钥
    if (!process.env.GEMINI_API_KEY) {
      return NextResponse.json(
        { success: false, error: 'Gemini API key not configured' },
        { status: 500 }
      );
    }

    // 生成卡片
    const result = await generateFlashcards(body);
    
    if (!result.success) {
      return NextResponse.json(result, { status: 500 });
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    );
  }
}
