import { Flashcard, Subject, ReviewSession, LearningStats } from '@/types';

// 本地存储键名
const STORAGE_KEYS = {
  FLASHCARDS: 'flashcards',
  SUBJECTS: 'subjects',
  REVIEW_SESSIONS: 'review_sessions',
  LEARNING_STATS: 'learning_stats',
} as const;

// 生成唯一ID
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 卡片存储操作
export class FlashcardStorage {
  static getAll(): Flashcard[] {
    if (typeof window === 'undefined') return [];
    
    const stored = localStorage.getItem(STORAGE_KEYS.FLASHCARDS);
    if (!stored) return [];
    
    try {
      const cards = JSON.parse(stored);
      return cards.map((card: any) => ({
        ...card,
        createdAt: new Date(card.createdAt),
        lastReviewed: card.lastReviewed ? new Date(card.lastReviewed) : undefined,
      }));
    } catch {
      return [];
    }
  }

  static save(cards: Flashcard[]): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(STORAGE_KEYS.FLASHCARDS, JSON.stringify(cards));
  }

  static add(card: Omit<Flashcard, 'id' | 'createdAt' | 'reviewCount' | 'correctCount'>): Flashcard {
    const newCard: Flashcard = {
      ...card,
      id: generateId(),
      createdAt: new Date(),
      reviewCount: 0,
      correctCount: 0,
    };

    const cards = this.getAll();
    cards.push(newCard);
    this.save(cards);
    
    return newCard;
  }

  static update(id: string, updates: Partial<Flashcard>): void {
    const cards = this.getAll();
    const index = cards.findIndex(card => card.id === id);
    
    if (index !== -1) {
      cards[index] = { ...cards[index], ...updates };
      this.save(cards);
    }
  }

  static delete(id: string): void {
    const cards = this.getAll();
    const filtered = cards.filter(card => card.id !== id);
    this.save(filtered);
  }

  static getBySubject(subject: string): Flashcard[] {
    return this.getAll().filter(card => card.subject === subject);
  }

  static getDueForReview(): Flashcard[] {
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    return this.getAll().filter(card => 
      !card.lastReviewed || card.lastReviewed < oneDayAgo
    );
  }
}

// 主题存储操作
export class SubjectStorage {
  static getAll(): Subject[] {
    if (typeof window === 'undefined') return [];
    
    const stored = localStorage.getItem(STORAGE_KEYS.SUBJECTS);
    if (!stored) return this.getDefaultSubjects();
    
    try {
      return JSON.parse(stored);
    } catch {
      return this.getDefaultSubjects();
    }
  }

  static save(subjects: Subject[]): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(STORAGE_KEYS.SUBJECTS, JSON.stringify(subjects));
  }

  static add(subject: Omit<Subject, 'id' | 'cardCount'>): Subject {
    const newSubject: Subject = {
      ...subject,
      id: generateId(),
      cardCount: 0,
    };

    const subjects = this.getAll();
    subjects.push(newSubject);
    this.save(subjects);
    
    return newSubject;
  }

  static updateCardCount(subjectName: string): void {
    const subjects = this.getAll();
    const cards = FlashcardStorage.getAll();
    
    subjects.forEach(subject => {
      subject.cardCount = cards.filter(card => card.subject === subject.name).length;
    });
    
    this.save(subjects);
  }

  private static getDefaultSubjects(): Subject[] {
    return [
      { id: '1', name: '语言学习', description: '外语词汇、语法等', color: '#3B82F6', cardCount: 0 },
      { id: '2', name: '编程技术', description: '编程概念、算法等', color: '#10B981', cardCount: 0 },
      { id: '3', name: '学术知识', description: '学科专业知识', color: '#8B5CF6', cardCount: 0 },
      { id: '4', name: '生活常识', description: '日常生活知识', color: '#F59E0B', cardCount: 0 },
      { id: '5', name: '其他', description: '其他类型知识', color: '#6B7280', cardCount: 0 },
    ];
  }
}

// 学习统计
export class StatsStorage {
  static get(): LearningStats {
    if (typeof window === 'undefined') {
      return this.getDefaultStats();
    }
    
    const stored = localStorage.getItem(STORAGE_KEYS.LEARNING_STATS);
    if (!stored) return this.getDefaultStats();
    
    try {
      const stats = JSON.parse(stored);
      // 转换日期字符串为Date对象
      Object.keys(stats.subjectStats).forEach(key => {
        if (stats.subjectStats[key].lastReviewed) {
          stats.subjectStats[key].lastReviewed = new Date(stats.subjectStats[key].lastReviewed);
        }
      });
      return stats;
    } catch {
      return this.getDefaultStats();
    }
  }

  static save(stats: LearningStats): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(STORAGE_KEYS.LEARNING_STATS, JSON.stringify(stats));
  }

  static updateAfterReview(subjectName: string, correct: boolean): void {
    const stats = this.get();
    
    stats.totalReviews++;
    if (correct) {
      stats.totalCards++;
    }
    
    // 更新主题统计
    if (!stats.subjectStats[subjectName]) {
      stats.subjectStats[subjectName] = {
        cardCount: 0,
        accuracy: 0,
        lastReviewed: new Date(),
      };
    }
    
    const subjectStat = stats.subjectStats[subjectName];
    subjectStat.lastReviewed = new Date();
    
    // 简单的准确率计算
    const oldAccuracy = subjectStat.accuracy;
    const reviewCount = subjectStat.cardCount + 1;
    subjectStat.accuracy = (oldAccuracy * subjectStat.cardCount + (correct ? 1 : 0)) / reviewCount;
    subjectStat.cardCount = reviewCount;
    
    // 重新计算总体准确率
    const totalCorrect = Object.values(stats.subjectStats).reduce(
      (sum, stat) => sum + (stat.accuracy * stat.cardCount), 0
    );
    stats.averageAccuracy = totalCorrect / stats.totalReviews;
    
    this.save(stats);
  }

  private static getDefaultStats(): LearningStats {
    return {
      totalCards: 0,
      totalReviews: 0,
      averageAccuracy: 0,
      streakDays: 0,
      subjectStats: {},
    };
  }
}
