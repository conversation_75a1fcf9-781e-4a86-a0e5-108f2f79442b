import { GoogleGenerativeAI } from '@google/generative-ai';
import { GenerateCardsRequest, GenerateCardsResponse } from '@/types';

// 初始化Gemini API
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

export async function generateFlashcards(request: GenerateCardsRequest): Promise<GenerateCardsResponse> {
  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash-exp' });

    const prompt = createPrompt(request);
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // 解析AI响应为卡片数据
    const cards = parseAIResponse(text, request.subject);

    return {
      cards,
      success: true,
    };
  } catch (error) {
    console.error('Error generating flashcards:', error);
    return {
      cards: [],
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

function createPrompt(request: GenerateCardsRequest): string {
  const { text, subject, difficulty = 'medium', cardCount = 5 } = request;

  return `
请根据以下文本内容生成${cardCount}张学习卡片，主题是"${subject}"，难度等级为"${difficulty}"。

文本内容：
${text}

要求：
1. 生成的问题应该涵盖文本的关键概念和重要信息
2. 问题要清晰明确，答案要准确完整
3. 根据难度等级调整问题复杂度：
   - easy: 基础概念和定义
   - medium: 概念理解和简单应用
   - hard: 深度分析和复杂应用
4. 每张卡片包含一个问题和对应答案
5. 为每张卡片添加2-3个相关标签

请按照以下JSON格式返回结果：
{
  "cards": [
    {
      "question": "问题内容",
      "answer": "答案内容",
      "difficulty": "${difficulty}",
      "subject": "${subject}",
      "tags": ["标签1", "标签2", "标签3"]
    }
  ]
}

只返回JSON格式的数据，不要包含其他文字说明。
`;
}

function parseAIResponse(text: string, subject: string) {
  try {
    // 清理响应文本，移除可能的markdown格式
    const cleanText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
    
    const parsed = JSON.parse(cleanText);
    
    if (parsed.cards && Array.isArray(parsed.cards)) {
      return parsed.cards.map((card: any) => ({
        question: card.question || '',
        answer: card.answer || '',
        difficulty: card.difficulty || 'medium',
        subject: card.subject || subject,
        tags: Array.isArray(card.tags) ? card.tags : [],
      }));
    }
    
    return [];
  } catch (error) {
    console.error('Error parsing AI response:', error);
    return [];
  }
}

// 分析文本难度
export function analyzeTextDifficulty(text: string): 'easy' | 'medium' | 'hard' {
  const wordCount = text.split(/\s+/).length;
  const avgWordLength = text.replace(/\s+/g, '').length / wordCount;
  const sentenceCount = text.split(/[.!?]+/).length;
  const avgSentenceLength = wordCount / sentenceCount;

  // 简单的难度评估算法
  if (avgWordLength < 5 && avgSentenceLength < 15) {
    return 'easy';
  } else if (avgWordLength < 7 && avgSentenceLength < 25) {
    return 'medium';
  } else {
    return 'hard';
  }
}
