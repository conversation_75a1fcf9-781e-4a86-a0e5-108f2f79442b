'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { GenerateCardsRequest } from '@/types';
import { Loader2, FileText, Upload } from 'lucide-react';

interface TextInputFormProps {
  onGenerate: (request: GenerateCardsRequest) => Promise<void>;
  isLoading: boolean;
  subjects: string[];
}

export function TextInputForm({ onGenerate, isLoading, subjects }: TextInputFormProps) {
  const [text, setText] = useState('');
  const [subject, setSubject] = useState('');
  const [customSubject, setCustomSubject] = useState('');
  const [difficulty, setDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium');
  const [cardCount, setCardCount] = useState(5);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!text.trim()) {
      alert('请输入要学习的文本内容');
      return;
    }

    const finalSubject = subject === 'custom' ? customSubject : subject;
    if (!finalSubject.trim()) {
      alert('请选择或输入学习主题');
      return;
    }

    await onGenerate({
      text: text.trim(),
      subject: finalSubject.trim(),
      difficulty,
      cardCount,
    });
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      const content = event.target?.result as string;
      setText(content);
    };
    reader.readAsText(file);
  };

  const wordCount = text.trim().split(/\s+/).filter(word => word.length > 0).length;

  return (
    <Card className="w-full max-w-2xl mx-auto animate-fade-in hover-lift">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="w-5 h-5" />
          创建学习卡片
        </CardTitle>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 文本输入区域 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">学习内容</label>
            <div className="relative">
              <Textarea
                value={text}
                onChange={(e) => setText(e.target.value)}
                placeholder="请输入要学习的文本内容，比如课程笔记、文章段落、概念定义等..."
                className="min-h-[150px] resize-none"
                disabled={isLoading}
              />
              <div className="absolute bottom-2 right-2 text-xs text-gray-500">
                {wordCount} 字
              </div>
            </div>
            
            {/* 文件上传 */}
            <div className="flex items-center gap-2">
              <input
                type="file"
                accept=".txt,.md"
                onChange={handleFileUpload}
                className="hidden"
                id="file-upload"
                disabled={isLoading}
              />
              <label
                htmlFor="file-upload"
                className="flex items-center gap-1 px-3 py-1 text-sm border rounded-md cursor-pointer hover:bg-gray-50 disabled:opacity-50"
              >
                <Upload className="w-4 h-4" />
                上传文本文件
              </label>
              <span className="text-xs text-gray-500">支持 .txt, .md 格式</span>
            </div>
          </div>

          {/* 主题选择 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">学习主题</label>
            <Select value={subject} onValueChange={setSubject} disabled={isLoading}>
              <SelectTrigger>
                <SelectValue placeholder="选择学习主题" />
              </SelectTrigger>
              <SelectContent>
                {subjects.map((subj) => (
                  <SelectItem key={subj} value={subj}>
                    {subj}
                  </SelectItem>
                ))}
                <SelectItem value="custom">自定义主题</SelectItem>
              </SelectContent>
            </Select>
            
            {subject === 'custom' && (
              <Input
                value={customSubject}
                onChange={(e) => setCustomSubject(e.target.value)}
                placeholder="输入自定义主题名称"
                disabled={isLoading}
              />
            )}
          </div>

          {/* 设置选项 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">难度等级</label>
              <Select 
                value={difficulty} 
                onValueChange={(value: 'easy' | 'medium' | 'hard') => setDifficulty(value)}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="easy">
                    <div className="flex items-center gap-2">
                      <Badge className="bg-green-100 text-green-800">简单</Badge>
                      <span className="text-sm">基础概念</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="medium">
                    <div className="flex items-center gap-2">
                      <Badge className="bg-yellow-100 text-yellow-800">中等</Badge>
                      <span className="text-sm">理解应用</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="hard">
                    <div className="flex items-center gap-2">
                      <Badge className="bg-red-100 text-red-800">困难</Badge>
                      <span className="text-sm">深度分析</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">卡片数量</label>
              <Select 
                value={cardCount.toString()} 
                onValueChange={(value) => setCardCount(parseInt(value))}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="3">3 张卡片</SelectItem>
                  <SelectItem value="5">5 张卡片</SelectItem>
                  <SelectItem value="8">8 张卡片</SelectItem>
                  <SelectItem value="10">10 张卡片</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 提交按钮 */}
          <Button
            type="submit"
            className="w-full transition-all duration-200 hover:scale-105 disabled:hover:scale-100"
            disabled={isLoading || !text.trim() || (!subject || (subject === 'custom' && !customSubject.trim()))}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                正在生成卡片...
              </>
            ) : (
              '生成学习卡片'
            )}
          </Button>

          {/* 提示信息 */}
          {wordCount > 0 && (
            <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
              <p>💡 提示：</p>
              <ul className="mt-1 space-y-1 text-xs">
                <li>• 文本长度适中时效果最佳（100-1000字）</li>
                <li>• 内容结构清晰有助于生成更好的问答</li>
                <li>• 可以包含定义、概念、步骤等不同类型的知识点</li>
              </ul>
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
}
