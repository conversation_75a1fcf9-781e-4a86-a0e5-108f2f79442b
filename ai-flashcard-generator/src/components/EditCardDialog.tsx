'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Flashcard } from '@/types';
import { X } from 'lucide-react';

interface EditCardDialogProps {
  card: Flashcard | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (card: Flashcard) => void;
  subjects: string[];
}

export function EditCardDialog({ card, isOpen, onClose, onSave, subjects }: EditCardDialogProps) {
  const [question, setQuestion] = useState('');
  const [answer, setAnswer] = useState('');
  const [subject, setSubject] = useState('');
  const [customSubject, setCustomSubject] = useState('');
  const [difficulty, setDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium');
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');

  // 当卡片变化时更新表单
  useEffect(() => {
    if (card) {
      setQuestion(card.question);
      setAnswer(card.answer);
      setSubject(subjects.includes(card.subject) ? card.subject : 'custom');
      setCustomSubject(subjects.includes(card.subject) ? '' : card.subject);
      setDifficulty(card.difficulty);
      setTags(card.tags);
    }
  }, [card, subjects]);

  const handleSave = () => {
    if (!card || !question.trim() || !answer.trim()) return;

    const finalSubject = subject === 'custom' ? customSubject : subject;
    if (!finalSubject.trim()) return;

    const updatedCard: Flashcard = {
      ...card,
      question: question.trim(),
      answer: answer.trim(),
      subject: finalSubject.trim(),
      difficulty,
      tags: tags.filter(tag => tag.trim().length > 0),
    };

    onSave(updatedCard);
    onClose();
  };

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  if (!card) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>编辑学习卡片</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 问题 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">问题</label>
            <Textarea
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              placeholder="输入问题内容..."
              className="min-h-[80px]"
            />
          </div>

          {/* 答案 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">答案</label>
            <Textarea
              value={answer}
              onChange={(e) => setAnswer(e.target.value)}
              placeholder="输入答案内容..."
              className="min-h-[80px]"
            />
          </div>

          {/* 主题和难度 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">主题</label>
              <Select value={subject} onValueChange={setSubject}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map((subj) => (
                    <SelectItem key={subj} value={subj}>
                      {subj}
                    </SelectItem>
                  ))}
                  <SelectItem value="custom">自定义主题</SelectItem>
                </SelectContent>
              </Select>
              
              {subject === 'custom' && (
                <Input
                  value={customSubject}
                  onChange={(e) => setCustomSubject(e.target.value)}
                  placeholder="输入自定义主题名称"
                />
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">难度</label>
              <Select 
                value={difficulty} 
                onValueChange={(value: 'easy' | 'medium' | 'hard') => setDifficulty(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="easy">简单</SelectItem>
                  <SelectItem value="medium">中等</SelectItem>
                  <SelectItem value="hard">困难</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 标签 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">标签</label>
            
            {/* 现有标签 */}
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-2">
                {tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {tag}
                    <button
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 hover:text-red-600"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}

            {/* 添加新标签 */}
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="输入标签名称..."
                className="flex-1"
              />
              <Button
                type="button"
                onClick={handleAddTag}
                variant="outline"
                disabled={!newTag.trim() || tags.includes(newTag.trim())}
              >
                添加
              </Button>
            </div>
          </div>

          {/* 卡片统计信息 */}
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm text-gray-600 space-y-1">
              <div>创建时间: {card.createdAt.toLocaleString()}</div>
              <div>复习次数: {card.reviewCount}</div>
              {card.reviewCount > 0 && (
                <div>正确率: {Math.round((card.correctCount / card.reviewCount) * 100)}%</div>
              )}
              {card.lastReviewed && (
                <div>上次复习: {card.lastReviewed.toLocaleString()}</div>
              )}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button 
            onClick={handleSave}
            disabled={!question.trim() || !answer.trim() || (!subject || (subject === 'custom' && !customSubject.trim()))}
          >
            保存修改
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
