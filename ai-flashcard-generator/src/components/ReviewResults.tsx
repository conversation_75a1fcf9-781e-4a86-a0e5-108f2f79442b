'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Clock, Target, TrendingUp, RotateCcw } from 'lucide-react';

interface ReviewResults {
  totalCards: number;
  correctAnswers: number;
  incorrectAnswers: number;
  accuracy: number;
  timeSpent: number;
}

interface ReviewResultsProps {
  results: ReviewResults;
  onRestart: () => void;
  onExit: () => void;
}

export function ReviewResults({ results, onRestart, onExit }: ReviewResultsProps) {
  const { totalCards, correctAnswers, incorrectAnswers, accuracy, timeSpent } = results;
  
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return minutes > 0 ? `${minutes}分${remainingSeconds}秒` : `${remainingSeconds}秒`;
  };

  const getAccuracyColor = (accuracy: number) => {
    if (accuracy >= 80) return 'text-green-600 bg-green-50 border-green-200';
    if (accuracy >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getAccuracyMessage = (accuracy: number) => {
    if (accuracy >= 90) return '优秀！继续保持！';
    if (accuracy >= 80) return '很好！再接再厉！';
    if (accuracy >= 60) return '不错，还有提升空间';
    return '需要加强复习哦';
  };

  const averageTimePerCard = totalCards > 0 ? Math.round(timeSpent / totalCards) : 0;

  return (
    <div className="w-full max-w-2xl mx-auto space-y-6">
      {/* 主要结果 */}
      <Card className={`border-2 ${getAccuracyColor(accuracy)}`}>
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {accuracy >= 80 ? (
              <CheckCircle className="w-16 h-16 text-green-500" />
            ) : (
              <Target className="w-16 h-16 text-yellow-500" />
            )}
          </div>
          <CardTitle className="text-2xl">复习完成！</CardTitle>
          <p className="text-lg font-medium mt-2">
            {getAccuracyMessage(accuracy)}
          </p>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 准确率 */}
          <div className="text-center">
            <div className="text-4xl font-bold mb-2">
              {Math.round(accuracy)}%
            </div>
            <p className="text-gray-600">准确率</p>
          </div>

          {/* 详细统计 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-900">{totalCards}</div>
              <p className="text-sm text-gray-600">总卡片数</p>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-900">{formatTime(timeSpent)}</div>
              <p className="text-sm text-gray-600">总用时</p>
            </div>
          </div>

          {/* 正确/错误统计 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center justify-center p-4 bg-green-50 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-500 mr-2" />
              <div>
                <div className="text-xl font-bold text-green-700">{correctAnswers}</div>
                <p className="text-sm text-green-600">答对</p>
              </div>
            </div>
            
            <div className="flex items-center justify-center p-4 bg-red-50 rounded-lg">
              <XCircle className="w-6 h-6 text-red-500 mr-2" />
              <div>
                <div className="text-xl font-bold text-red-700">{incorrectAnswers}</div>
                <p className="text-sm text-red-600">答错</p>
              </div>
            </div>
          </div>

          {/* 效率统计 */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-5 h-5 text-blue-600" />
              <span className="font-medium text-blue-900">效率统计</span>
            </div>
            <div className="text-sm text-blue-700 space-y-1">
              <div>平均每张卡片用时: {averageTimePerCard}秒</div>
              <div>
                效率评价: {
                  averageTimePerCard <= 10 ? '很快' :
                  averageTimePerCard <= 20 ? '正常' :
                  averageTimePerCard <= 30 ? '较慢' : '需要提高'
                }
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 建议和鼓励 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            学习建议
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            {accuracy >= 90 && (
              <div className="flex items-start gap-2 p-3 bg-green-50 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-green-900">表现优秀！</p>
                  <p className="text-green-700">你已经很好地掌握了这些知识点，可以尝试学习更高难度的内容。</p>
                </div>
              </div>
            )}
            
            {accuracy >= 60 && accuracy < 90 && (
              <div className="flex items-start gap-2 p-3 bg-yellow-50 rounded-lg">
                <Target className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-yellow-900">继续努力！</p>
                  <p className="text-yellow-700">建议重点复习答错的卡片，加深对知识点的理解。</p>
                </div>
              </div>
            )}
            
            {accuracy < 60 && (
              <div className="flex items-start gap-2 p-3 bg-red-50 rounded-lg">
                <XCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium text-red-900">需要加强</p>
                  <p className="text-red-700">建议重新学习相关内容，然后再次进行复习练习。</p>
                </div>
              </div>
            )}

            <div className="border-t pt-3 mt-3">
              <p className="text-gray-600">
                💡 <strong>小贴士：</strong>定期复习是巩固记忆的关键。建议每天花10-15分钟复习之前学过的内容。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <div className="flex gap-4">
        <Button
          onClick={onRestart}
          className="flex-1"
          variant="outline"
        >
          <RotateCcw className="w-4 h-4 mr-2" />
          再次复习
        </Button>
        
        <Button
          onClick={onExit}
          className="flex-1"
        >
          返回主页
        </Button>
      </div>
    </div>
  );
}
