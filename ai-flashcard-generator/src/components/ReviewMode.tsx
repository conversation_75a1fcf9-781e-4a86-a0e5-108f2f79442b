'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { FlashcardComponent } from './FlashcardComponent';
import { Flashcard } from '@/types';
import { RotateCcw, CheckCircle, XCircle, Clock, Target } from 'lucide-react';

interface ReviewModeProps {
  cards: Flashcard[];
  onReview: (id: string, correct: boolean) => void;
  onComplete: (results: ReviewResults) => void;
  onExit: () => void;
}

interface ReviewResults {
  totalCards: number;
  correctAnswers: number;
  incorrectAnswers: number;
  accuracy: number;
  timeSpent: number;
}

export function ReviewMode({ cards, onReview, onComplete, onExit }: ReviewModeProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [reviewedCards, setReviewedCards] = useState<Set<string>>(new Set());
  const [correctCards, setCorrectCards] = useState<Set<string>>(new Set());
  const [startTime] = useState(Date.now());
  const [sessionCards, setSessionCards] = useState<Flashcard[]>([]);

  // 初始化复习卡片（优先显示需要复习的卡片）
  useEffect(() => {
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    // 按优先级排序：未复习过的 > 很久没复习的 > 最近复习过的
    const sortedCards = [...cards].sort((a, b) => {
      if (!a.lastReviewed && !b.lastReviewed) return 0;
      if (!a.lastReviewed) return -1;
      if (!b.lastReviewed) return 1;
      
      const aAccuracy = a.reviewCount > 0 ? a.correctCount / a.reviewCount : 0;
      const bAccuracy = b.reviewCount > 0 ? b.correctCount / b.reviewCount : 0;
      
      // 准确率低的优先
      if (aAccuracy !== bAccuracy) return aAccuracy - bAccuracy;
      
      // 最后复习时间早的优先
      return a.lastReviewed.getTime() - b.lastReviewed.getTime();
    });
    
    setSessionCards(sortedCards);
  }, [cards]);

  const currentCard = sessionCards[currentIndex];
  const progress = sessionCards.length > 0 ? ((currentIndex + 1) / sessionCards.length) * 100 : 0;
  const reviewedCount = reviewedCards.size;
  const correctCount = correctCards.size;

  const handleCardReview = (id: string, correct: boolean) => {
    onReview(id, correct);
    
    setReviewedCards(prev => new Set([...prev, id]));
    if (correct) {
      setCorrectCards(prev => new Set([...prev, id]));
    }

    // 移动到下一张卡片或完成复习
    if (currentIndex < sessionCards.length - 1) {
      setCurrentIndex(currentIndex + 1);
    } else {
      // 复习完成
      const timeSpent = Date.now() - startTime;
      const results: ReviewResults = {
        totalCards: sessionCards.length,
        correctAnswers: correctCount + (correct ? 1 : 0),
        incorrectAnswers: sessionCards.length - correctCount - (correct ? 1 : 0),
        accuracy: ((correctCount + (correct ? 1 : 0)) / sessionCards.length) * 100,
        timeSpent: Math.round(timeSpent / 1000), // 转换为秒
      };
      onComplete(results);
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentIndex < sessionCards.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  if (sessionCards.length === 0) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <CheckCircle className="w-12 h-12 text-green-500 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">没有需要复习的卡片</h3>
          <p className="text-gray-500 text-center mb-4">
            所有卡片都已经在最近复习过了！
          </p>
          <Button onClick={onExit}>返回</Button>
        </CardContent>
      </Card>
    );
  }

  if (!currentCard) return null;

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* 复习进度 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              复习模式
            </CardTitle>
            <Button variant="outline" onClick={onExit}>
              退出复习
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>进度: {currentIndex + 1} / {sessionCards.length}</span>
            <span>已复习: {reviewedCount}</span>
            <span>正确: {correctCount}</span>
          </div>
          <Progress value={progress} className="w-full" />
        </CardContent>
      </Card>

      {/* 当前卡片 */}
      <div className="flex justify-center">
        <FlashcardComponent
          card={currentCard}
          showActions={false}
          onReview={handleCardReview}
        />
      </div>

      {/* 导航按钮 */}
      <div className="flex justify-center gap-4">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentIndex === 0}
        >
          上一张
        </Button>
        
        <div className="flex items-center gap-2 px-4 py-2 bg-gray-100 rounded-lg">
          <Clock className="w-4 h-4 text-gray-500" />
          <span className="text-sm text-gray-600">
            {Math.round((Date.now() - startTime) / 1000)}s
          </span>
        </div>

        <Button
          variant="outline"
          onClick={handleNext}
          disabled={currentIndex === sessionCards.length - 1}
        >
          下一张
        </Button>
      </div>

      {/* 复习提示 */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
              <RotateCcw className="w-4 h-4 text-blue-600" />
            </div>
            <div className="space-y-1">
              <h4 className="font-medium text-blue-900">复习提示</h4>
              <p className="text-sm text-blue-700">
                仔细阅读问题，在心中思考答案，然后点击"显示答案"查看正确答案。
                根据你的回答情况选择"答对了"或"答错了"。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 卡片信息 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-4">
              <Badge className={
                currentCard.difficulty === 'easy' ? 'bg-green-100 text-green-800' :
                currentCard.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }>
                {currentCard.difficulty === 'easy' ? '简单' :
                 currentCard.difficulty === 'medium' ? '中等' : '困难'}
              </Badge>
              <span>复习次数: {currentCard.reviewCount}</span>
              {currentCard.reviewCount > 0 && (
                <span>
                  历史正确率: {Math.round((currentCard.correctCount / currentCard.reviewCount) * 100)}%
                </span>
              )}
            </div>
            {currentCard.lastReviewed && (
              <span>
                上次复习: {currentCard.lastReviewed.toLocaleDateString()}
              </span>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
