'use client';

import { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { FlashcardComponent } from './FlashcardComponent';
import { Flashcard } from '@/types';
import { Search, Filter, BookOpen, Clock, TrendingUp } from 'lucide-react';

interface FlashcardListProps {
  cards: Flashcard[];
  onEdit: (card: Flashcard) => void;
  onDelete: (id: string) => void;
  onReview: (id: string, correct: boolean) => void;
}

export function FlashcardList({ cards, onEdit, onDelete, onReview }: FlashcardListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterSubject, setFilterSubject] = useState('all');
  const [filterDifficulty, setFilterDifficulty] = useState('all');
  const [sortBy, setSortBy] = useState<'created' | 'reviewed' | 'accuracy'>('created');

  // 获取所有主题
  const subjects = Array.from(new Set(cards.map(card => card.subject)));

  // 过滤和排序卡片
  const filteredAndSortedCards = cards
    .filter(card => {
      const matchesSearch = card.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           card.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           card.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesSubject = filterSubject === 'all' || card.subject === filterSubject;
      const matchesDifficulty = filterDifficulty === 'all' || card.difficulty === filterDifficulty;
      
      return matchesSearch && matchesSubject && matchesDifficulty;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'created':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'reviewed':
          const aReviewed = a.lastReviewed?.getTime() || 0;
          const bReviewed = b.lastReviewed?.getTime() || 0;
          return bReviewed - aReviewed;
        case 'accuracy':
          const aAccuracy = a.reviewCount > 0 ? a.correctCount / a.reviewCount : 0;
          const bAccuracy = b.reviewCount > 0 ? b.correctCount / b.reviewCount : 0;
          return bAccuracy - aAccuracy;
        default:
          return 0;
      }
    });

  // 统计信息
  const stats = {
    total: cards.length,
    reviewed: cards.filter(card => card.reviewCount > 0).length,
    needReview: cards.filter(card => {
      if (!card.lastReviewed) return true;
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      return card.lastReviewed < oneDayAgo;
    }).length,
  };

  if (cards.length === 0) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <BookOpen className="w-12 h-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">还没有学习卡片</h3>
          <p className="text-gray-500 text-center">
            输入学习内容并生成你的第一张学习卡片吧！
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* 统计信息 */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <Card className="hover-lift animate-fade-in">
          <CardContent className="flex items-center p-4">
            <BookOpen className="w-8 h-8 text-blue-500 mr-3" />
            <div>
              <p className="text-2xl font-bold">{stats.total}</p>
              <p className="text-sm text-gray-600">总卡片数</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center p-4">
            <TrendingUp className="w-8 h-8 text-green-500 mr-3" />
            <div>
              <p className="text-2xl font-bold">{stats.reviewed}</p>
              <p className="text-sm text-gray-600">已复习</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center p-4">
            <Clock className="w-8 h-8 text-orange-500 mr-3" />
            <div>
              <p className="text-2xl font-bold">{stats.needReview}</p>
              <p className="text-sm text-gray-600">待复习</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和过滤 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            筛选和搜索
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索问题、答案或标签..."
              className="pl-10"
            />
          </div>

          {/* 过滤选项 */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">主题</label>
              <Select value={filterSubject} onValueChange={setFilterSubject}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部主题</SelectItem>
                  {subjects.map(subject => (
                    <SelectItem key={subject} value={subject}>
                      {subject}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">难度</label>
              <Select value={filterDifficulty} onValueChange={setFilterDifficulty}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部难度</SelectItem>
                  <SelectItem value="easy">简单</SelectItem>
                  <SelectItem value="medium">中等</SelectItem>
                  <SelectItem value="hard">困难</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">排序</label>
              <Select value={sortBy} onValueChange={(value: 'created' | 'reviewed' | 'accuracy') => setSortBy(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created">创建时间</SelectItem>
                  <SelectItem value="reviewed">复习时间</SelectItem>
                  <SelectItem value="accuracy">正确率</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 卡片列表 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">
            学习卡片 ({filteredAndSortedCards.length})
          </h3>
          {searchTerm && (
            <Badge variant="outline">
              搜索: "{searchTerm}"
            </Badge>
          )}
        </div>

        {filteredAndSortedCards.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-8">
              <Search className="w-8 h-8 text-gray-400 mb-2" />
              <p className="text-gray-500">没有找到匹配的卡片</p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredAndSortedCards.map(card => (
              <FlashcardComponent
                key={card.id}
                card={card}
                onEdit={onEdit}
                onDelete={onDelete}
                onReview={onReview}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
