'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Flashcard } from '@/types';
import { RotateCcw, Eye, EyeOff, Edit, Trash2 } from 'lucide-react';

interface FlashcardComponentProps {
  card: Flashcard;
  showActions?: boolean;
  onEdit?: (card: Flashcard) => void;
  onDelete?: (id: string) => void;
  onReview?: (id: string, correct: boolean) => void;
}

export function FlashcardComponent({ 
  card, 
  showActions = true, 
  onEdit, 
  onDelete, 
  onReview 
}: FlashcardComponentProps) {
  const [isFlipped, setIsFlipped] = useState(false);
  const [showAnswer, setShowAnswer] = useState(false);

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
    setShowAnswer(!showAnswer);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '简单';
      case 'medium': return '中等';
      case 'hard': return '困难';
      default: return difficulty;
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto transition-all duration-300 hover:shadow-lg hover-lift animate-fade-in">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">{card.subject}</CardTitle>
          <Badge className={getDifficultyColor(card.difficulty)}>
            {getDifficultyText(card.difficulty)}
          </Badge>
        </div>
        {card.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {card.tags.map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 卡片内容区域 */}
        <div className="min-h-[120px] flex items-center justify-center p-4 bg-gray-50 rounded-lg relative overflow-hidden">
          <div className={`transition-all duration-300 ${showAnswer ? 'animate-fade-in' : 'animate-fade-in'}`}>
            {!showAnswer ? (
              <div className="text-center">
                <h3 className="font-medium text-gray-900 mb-2">问题</h3>
                <p className="text-gray-700">{card.question}</p>
              </div>
            ) : (
              <div className="text-center">
                <h3 className="font-medium text-gray-900 mb-2">答案</h3>
                <p className="text-gray-700">{card.answer}</p>
              </div>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-col gap-2">
          <Button
            onClick={handleFlip}
            variant="outline"
            className="w-full transition-all duration-200 hover:scale-105"
          >
            {showAnswer ? (
              <>
                <EyeOff className="w-4 h-4 mr-2" />
                隐藏答案
              </>
            ) : (
              <>
                <Eye className="w-4 h-4 mr-2" />
                显示答案
              </>
            )}
          </Button>

          {showAnswer && onReview && (
            <div className="flex gap-2 animate-fade-in">
              <Button
                onClick={() => onReview(card.id, false)}
                variant="outline"
                className="flex-1 text-red-600 hover:text-red-700 transition-all duration-200 hover:scale-105"
              >
                答错了
              </Button>
              <Button
                onClick={() => onReview(card.id, true)}
                className="flex-1 bg-green-600 hover:bg-green-700 transition-all duration-200 hover:scale-105"
              >
                答对了
              </Button>
            </div>
          )}

          {showActions && (
            <div className="flex gap-2 pt-2 border-t">
              {onEdit && (
                <Button
                  onClick={() => onEdit(card)}
                  variant="ghost"
                  size="sm"
                  className="flex-1"
                >
                  <Edit className="w-4 h-4 mr-1" />
                  编辑
                </Button>
              )}
              {onDelete && (
                <Button
                  onClick={() => onDelete(card.id)}
                  variant="ghost"
                  size="sm"
                  className="flex-1 text-red-600 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  删除
                </Button>
              )}
            </div>
          )}
        </div>

        {/* 学习统计 */}
        {card.reviewCount > 0 && (
          <div className="text-xs text-gray-500 pt-2 border-t">
            <div className="flex justify-between">
              <span>复习次数: {card.reviewCount}</span>
              <span>正确率: {Math.round((card.correctCount / card.reviewCount) * 100)}%</span>
            </div>
            {card.lastReviewed && (
              <div className="mt-1">
                上次复习: {card.lastReviewed.toLocaleDateString()}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
