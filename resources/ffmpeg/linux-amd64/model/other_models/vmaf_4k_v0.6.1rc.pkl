(dp0
S'param_dict'
p1
(dp2
S'C'
p3
F4.0
sS'score_transform'
p4
(dp5
S'p2'
p6
F-0.00705305
sS'p0'
p7
F1.70674692
sS'p1'
p8
F1.72643844
ssS'norm_type'
p9
S'clip_0to1'
p10
sS'score_clip'
p11
(lp12
F0.0
aF110.0
asS'nu'
p13
F0.9
sS'gamma'
p14
F0.04
ssS'model_dict'
p15
(dp16
S'model'
p17
Nsg4
g5
sg9
S'linear_rescale'
p18
sg11
g12
sS'feature_names'
p19
(lp20
S'VMAF_feature_adm2_score'
p21
aS'VMAF_feature_motion2_score'
p22
aS'VMAF_feature_vif_scale0_score'
p23
aS'VMAF_feature_vif_scale1_score'
p24
aS'VMAF_feature_vif_scale2_score'
p25
aS'VMAF_feature_vif_scale3_score'
p26
asS'intercepts'
p27
(lp28
F-0.23202029036329092
aF-0.7703350310526216
aF-0.010411981862966059
aF-0.09603743694887917
aF-0.21890356649141152
aF-0.35835059999617175
aF-0.6161060516582791
asS'model_type'
p29
S'LIBSVMNUSVR'
p30
sS'slopes'
p31
(lp32
F0.244201478446933
aF1.7697551475258635
aF0.061831493128738056
aF1.0960374437054892
aF1.2189036205165853
aF1.3583508615652835
aF1.6161065113575923
asS'feature_dict'
p33
(dp34
S'VMAF_feature'
p35
(lp36
S'vif_scale0'
p37
aS'vif_scale1'
p38
aS'vif_scale2'
p39
aS'vif_scale3'
p40
aS'adm2'
p41
aS'motion2'
p42
asss.