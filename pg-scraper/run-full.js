const <PERSON><PERSON><PERSON><PERSON><PERSON>craper = require('./scraper');

async function runFullScraper() {
    const scraper = new <PERSON><PERSON><PERSON>amScraper();
    
    try {
        console.log('🚀 Starting full Paul <PERSON> scraper...\n');
        console.log('This will download all articles and generate an EPUB.');
        console.log('The process may take several minutes...\n');
        
        await scraper.init();
        
        // 获取文章列表
        const articles = await scraper.fetchArticleList();
        console.log(`📚 Found ${articles.length} articles to download\n`);
        
        // 限制并发请求
        const contents = [];
        const batchSize = 3; // 每次处理3篇文章
        
        for (let i = 0; i < articles.length; i += batchSize) {
            const batch = articles.slice(i, i + batchSize);
            console.log(`📥 Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(articles.length/batchSize)} (articles ${i+1}-${Math.min(i+batchSize, articles.length)})`);
            
            const batchPromises = batch.map(article => scraper.fetchArticleContent(article));
            const batchResults = await Promise.allSettled(batchPromises);
            
            batchResults.forEach(result => {
                if (result.status === 'fulfilled' && result.value) {
                    contents.push(result.value);
                }
            });
            
            console.log(`✅ Completed batch. Total articles downloaded: ${contents.length}`);
            
            // 延迟避免过于频繁的请求
            if (i + batchSize < articles.length) {
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
        console.log(`\n📖 Successfully downloaded ${contents.length} articles`);
        
        // 生成EPUB
        if (contents.length > 0) {
            console.log('\n📚 Generating EPUB...');
            await scraper.generateEPUB(contents);
            
            console.log('\n✅ All done!');
            console.log(`📁 Markdown files: ${scraper.markdownDir}`);
            console.log(`📚 EPUB file: ${scraper.outputDir}/paul-graham-essays.epub`);
            console.log(`📊 Total articles: ${contents.length}`);
        } else {
            console.log('❌ No articles were downloaded successfully');
        }
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    runFullScraper();
}

module.exports = runFullScraper;