const axios = require('axios');
const cheerio = require('cheerio');
const TurndownService = require('turndown');
const fs = require('fs-extra');
const path = require('path');
const archiver = require('archiver');

class PaulGrahamScraper {
    constructor() {
        this.baseUrl = 'http://www.paulgraham.com';
        this.articlesUrl = 'http://www.paulgraham.com/articles.html';
        this.turndownService = new TurndownService({
            headingStyle: 'atx',
            bulletListMarker: '-',
            codeBlockStyle: 'fenced'
        });
        this.articles = [];
        this.outputDir = path.join(__dirname, 'output');
        this.markdownDir = path.join(this.outputDir, 'markdown');
    }

    async init() {
        await fs.ensureDir(this.outputDir);
        await fs.ensureDir(this.markdownDir);
    }

    async fetchArticleList() {
        try {
            console.log('Fetching article list from:', this.articlesUrl);
            const response = await axios.get(this.articlesUrl);
            const $ = cheerio.load(response.data);
            
            const articles = [];
            
            // <PERSON>的文章页面结构：在table中的链接
            $('table').find('a').each((index, element) => {
                const href = $(element).attr('href');
                const title = $(element).text().trim();
                
                if (href && title && href.endsWith('.html') && !href.includes('http')) {
                    articles.push({
                        title: title,
                        url: this.baseUrl + '/' + href,
                        filename: href.replace('.html', '')
                    });
                }
            });
            
            this.articles = articles;
            console.log(`Found ${articles.length} articles`);
            return articles;
        } catch (error) {
            console.error('Error fetching article list:', error.message);
            throw error;
        }
    }

    async fetchArticleContent(article) {
        try {
            console.log(`Fetching: ${article.title}`);
            const response = await axios.get(article.url);
            const $ = cheerio.load(response.data);
            
            // 移除脚本和样式
            $('script, style').remove();
            
            // 获取主要内容，通常在body或特定容器中
            let content = $('body').html();
            
            // 清理内容
            content = content.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');
            content = content.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');
            
            // 转换为markdown
            const markdown = this.turndownService.turndown(content);
            
            // 添加标题
            const finalMarkdown = `# ${article.title}\n\n${markdown}`;
            
            // 保存markdown文件
            const markdownPath = path.join(this.markdownDir, `${article.filename}.md`);
            await fs.writeFile(markdownPath, finalMarkdown, 'utf8');
            
            return {
                title: article.title,
                content: finalMarkdown,
                filename: article.filename
            };
        } catch (error) {
            console.error(`Error fetching article ${article.title}:`, error.message);
            return null;
        }
    }

    async scrapeAllArticles() {
        const articles = await this.fetchArticleList();
        const contents = [];
        
        for (const article of articles) {
            const content = await this.fetchArticleContent(article);
            if (content) {
                contents.push(content);
            }
            // 添加延迟避免频繁请求
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        return contents;
    }

    async generateEPUB(contents) {
        try {
            console.log('Generating EPUB...');
            
            const epubDir = path.join(this.outputDir, 'epub_temp');
            const metaInfDir = path.join(epubDir, 'META-INF');
            const oebpsDir = path.join(epubDir, 'OEBPS');
            
            await fs.ensureDir(epubDir);
            await fs.ensureDir(metaInfDir);
            await fs.ensureDir(oebpsDir);
            
            // 创建mimetype文件
            await fs.writeFile(path.join(epubDir, 'mimetype'), 'application/epub+zip');
            
            // 创建META-INF/container.xml
            const containerXml = `<?xml version="1.0" encoding="UTF-8"?>
<container version="1.0" xmlns="urn:oasis:names:tc:opendocument:xmlns:container">
    <rootfiles>
        <rootfile full-path="OEBPS/content.opf" media-type="application/oebps-package+xml"/>
    </rootfiles>
</container>`;
            await fs.writeFile(path.join(metaInfDir, 'container.xml'), containerXml);
            
            // 创建OEBPS/content.opf
            const contentOpf = this.generateContentOpf(contents);
            await fs.writeFile(path.join(oebpsDir, 'content.opf'), contentOpf);
            
            // 创建OEBPS/toc.ncx
            const tocNcx = this.generateTocNcx(contents);
            await fs.writeFile(path.join(oebpsDir, 'toc.ncx'), tocNcx);
            
            // 创建目录页
            const tocHtml = this.generateTocHtml(contents);
            await fs.writeFile(path.join(oebpsDir, 'toc.html'), tocHtml);
            
            // 转换markdown为HTML并保存
            for (const article of contents) {
                const html = this.markdownToHtml(article.content, article.title);
                await fs.writeFile(path.join(oebpsDir, `${article.filename}.html`), html);
            }
            
            // 创建EPUB压缩包
            const epubPath = path.join(this.outputDir, 'paul-graham-essays.epub');
            await this.createEpubArchive(epubDir, epubPath);
            
            // 清理临时文件
            await fs.remove(epubDir);
            
            console.log('EPUB generated successfully!');
            console.log('File saved to:', epubPath);
            
        } catch (error) {
            console.error('Error generating EPUB:', error.message);
            throw error;
        }
    }
    
    generateContentOpf(contents) {
        const itemsManifest = contents.map(article => 
            `    <item id="${article.filename}" href="${article.filename}.html" media-type="application/xhtml+xml"/>`
        ).join('\n');
        
        const itemsSpine = contents.map(article => 
            `    <itemref idref="${article.filename}"/>`
        ).join('\n');
        
        return `<?xml version="1.0" encoding="UTF-8"?>
<package version="2.0" xmlns="http://www.idpf.org/2007/opf" unique-identifier="bookid">
    <metadata xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:opf="http://www.idpf.org/2007/opf">
        <dc:title>Paul Graham Essays Collection</dc:title>
        <dc:creator>Paul Graham</dc:creator>
        <dc:language>en</dc:language>
        <dc:identifier id="bookid">paul-graham-essays</dc:identifier>
        <dc:description>Complete collection of Paul Graham's essays</dc:description>
        <dc:publisher>Scraped Collection</dc:publisher>
        <dc:date>${new Date().toISOString().split('T')[0]}</dc:date>
    </metadata>
    <manifest>
        <item id="ncx" href="toc.ncx" media-type="application/x-dtbncx+xml"/>
        <item id="toc" href="toc.html" media-type="application/xhtml+xml"/>
${itemsManifest}
    </manifest>
    <spine toc="ncx">
        <itemref idref="toc"/>
${itemsSpine}
    </spine>
</package>`;
    }
    
    generateTocNcx(contents) {
        const navPoints = contents.map((article, index) => 
            `    <navPoint id="navpoint-${index + 1}" playOrder="${index + 1}">
            <navLabel><text>${article.title}</text></navLabel>
            <content src="${article.filename}.html"/>
        </navPoint>`
        ).join('\n');
        
        return `<?xml version="1.0" encoding="UTF-8"?>
<ncx version="2005-1" xmlns="http://www.daisy.org/z3986/2005/ncx/">
    <head>
        <meta name="dtb:uid" content="paul-graham-essays"/>
        <meta name="dtb:depth" content="1"/>
        <meta name="dtb:totalPageCount" content="0"/>
        <meta name="dtb:maxPageNumber" content="0"/>
    </head>
    <docTitle>
        <text>Paul Graham Essays Collection</text>
    </docTitle>
    <navMap>
        <navPoint id="navpoint-0" playOrder="0">
            <navLabel><text>Table of Contents</text></navLabel>
            <content src="toc.html"/>
        </navPoint>
${navPoints}
    </navMap>
</ncx>`;
    }
    
    generateTocHtml(contents) {
        const tocItems = contents.map(article => 
            `        <li><a href="${article.filename}.html">${article.title}</a></li>`
        ).join('\n');
        
        return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>Table of Contents</title>
    <meta charset="utf-8"/>
</head>
<body>
    <h1>Paul Graham Essays Collection</h1>
    <h2>Table of Contents</h2>
    <ul>
${tocItems}
    </ul>
</body>
</html>`;
    }
    
    markdownToHtml(markdown, title) {
        // 简单的markdown到HTML转换
        let html = markdown
            .replace(/^# (.+)$/gm, '<h1>$1</h1>')
            .replace(/^## (.+)$/gm, '<h2>$1</h2>')
            .replace(/^### (.+)$/gm, '<h3>$1</h3>')
            .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.+?)\*/g, '<em>$1</em>')
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br/>');
        
        // 包装在段落中
        html = '<p>' + html + '</p>';
        
        return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <title>${title}</title>
    <meta charset="utf-8"/>
    <style>
        body { font-family: serif; line-height: 1.6; margin: 2em; }
        h1, h2, h3 { color: #333; }
        p { margin: 1em 0; }
    </style>
</head>
<body>
    ${html}
</body>
</html>`;
    }
    
    async createEpubArchive(sourceDir, outputPath) {
        return new Promise((resolve, reject) => {
            const output = fs.createWriteStream(outputPath);
            const archive = archiver('zip', {
                zlib: { level: 9 }
            });
            
            output.on('close', () => {
                console.log(`EPUB created: ${archive.pointer()} total bytes`);
                resolve();
            });
            
            archive.on('error', (err) => {
                reject(err);
            });
            
            archive.pipe(output);
            
            // 添加mimetype文件（不压缩）
            archive.file(path.join(sourceDir, 'mimetype'), { name: 'mimetype', store: true });
            
            // 添加其他文件
            archive.directory(path.join(sourceDir, 'META-INF'), 'META-INF');
            archive.directory(path.join(sourceDir, 'OEBPS'), 'OEBPS');
            
            archive.finalize();
        });
    }

    async run() {
        try {
            await this.init();
            const contents = await this.scrapeAllArticles();
            await this.generateEPUB(contents);
            
            console.log('\n✅ All done!');
            console.log(`📁 Markdown files: ${this.markdownDir}`);
            console.log(`📚 EPUB file: ${path.join(this.outputDir, 'paul-graham-essays.epub')}`);
            
        } catch (error) {
            console.error('❌ Error:', error.message);
            process.exit(1);
        }
    }
}

// 运行爬虫
if (require.main === module) {
    const scraper = new PaulGrahamScraper();
    scraper.run();
}

module.exports = PaulGrahamScraper;