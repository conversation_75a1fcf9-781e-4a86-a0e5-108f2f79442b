const <PERSON><PERSON>rahamScraper = require('./scraper');

async function testEpubGeneration() {
    const scraper = new <PERSON><PERSON>rahamScraper();
    
    try {
        console.log('🧪 Testing EPUB generation with existing markdown...\n');
        
        await scraper.init();
        
        // 使用现有的markdown文件测试EPUB生成
        const fs = require('fs-extra');
        const path = require('path');
        
        const markdownPath = path.join(__dirname, 'output', 'markdown', 'greatwork.md');
        const content = await fs.readFile(markdownPath, 'utf8');
        
        const testContents = [{
            title: 'How to Do Great Work',
            content: content,
            filename: 'greatwork'
        }];
        
        console.log('Generating EPUB with 1 article...');
        await scraper.generateEPUB(testContents);
        
        console.log('✅ EPUB generation test passed!');
        
    } catch (error) {
        console.error('❌ EPUB generation test failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    testEpubGeneration();
}

module.exports = testEpubGeneration;