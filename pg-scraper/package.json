{"name": "pg-scraper", "version": "1.0.0", "description": "<PERSON> articles scraper and EPUB generator", "main": "scraper.js", "scripts": {"start": "node run-full.js", "test": "node test.js", "test-epub": "node test-epub.js"}, "dependencies": {"archiver": "^7.0.1", "axios": "^1.7.7", "cheerio": "^1.0.0-rc.12", "fs-extra": "^11.2.0", "turndown": "^7.1.2"}, "keywords": ["scraper", "epub", "paul-graham"], "author": "", "license": "MIT"}