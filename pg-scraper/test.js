const <PERSON><PERSON><PERSON><PERSON><PERSON>raper = require('./scraper');

async function test() {
    const scraper = new <PERSON><PERSON>raper();
    
    try {
        console.log('🧪 Testing Paul Graham scraper...\n');
        
        // 测试获取文章列表
        console.log('1. Testing article list fetching...');
        await scraper.init();
        const articles = await scraper.fetchArticleList();
        console.log(`✅ Found ${articles.length} articles`);
        
        if (articles.length > 0) {
            console.log('First few articles:');
            articles.slice(0, 3).forEach((article, index) => {
                console.log(`   ${index + 1}. ${article.title} - ${article.url}`);
            });
        }
        
        // 测试获取单篇文章内容
        if (articles.length > 0) {
            console.log('\n2. Testing single article content fetching...');
            const firstArticle = articles[0];
            const content = await scraper.fetchArticleContent(firstArticle);
            
            if (content) {
                console.log(`✅ Successfully fetched: ${content.title}`);
                console.log(`   Content length: ${content.content.length} characters`);
                console.log(`   First 100 chars: ${content.content.substring(0, 100)}...`);
            } else {
                console.log('❌ Failed to fetch article content');
            }
        }
        
        console.log('\n✅ Basic tests passed!');
        console.log('\n🚀 To run the full scraper, use: npm start');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    test();
}

module.exports = test;