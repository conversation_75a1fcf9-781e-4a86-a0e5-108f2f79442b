# <PERSON> Essay Scraper & EPUB Generator

这个工具可以抓取<PERSON>的所有文章，转换为Markdown格式，并生成EPUB电子书。

## 功能特点

- 🕷️ 自动抓取<PERSON>网站的所有文章
- 📝 将HTML内容转换为Markdown格式
- 📚 生成完整的EPUB电子书
- 💾 保存单独的Markdown文件用于备份
- ⏰ 包含请求延迟，避免过于频繁的请求

## 安装

```bash
# 进入项目目录
cd pg-scraper

# 安装依赖
npm install
```

## 使用方法

### 快速开始

```bash
# 运行完整的爬虫和EPUB生成（推荐）
npm start
```

### 其他命令

```bash
# 运行基本功能测试
npm test

# 测试EPUB生成功能
npm run test-epub
```

## 输出文件

运行完成后，会在 `output` 目录下生成：

- `paul-graham-essays.epub` - 完整的EPUB电子书
- `markdown/` - 包含所有文章的Markdown文件

## 工作原理

1. **获取文章列表**：从 `http://www.paulgraham.com/articles.html` 抓取所有文章链接
2. **批量下载**：分批下载文章内容，避免过于频繁的请求
3. **内容清理**：移除JavaScript、CSS等无关内容
4. **转换格式**：使用Turndown将HTML转换为Markdown
5. **生成EPUB**：创建标准的EPUB文件结构并压缩

## 特性

- 📊 **智能批处理**：分批下载文章，避免服务器负载过重
- 🔄 **错误恢复**：单篇文章失败不会影响整个流程
- 📱 **标准格式**：生成的EPUB兼容所有主流电子书阅读器
- 🎨 **美观排版**：包含目录、章节导航和CSS样式

## 注意事项

- 程序会在每次请求间添加1秒延迟，避免对服务器造成过大负担
- 如果某篇文章下载失败，程序会继续处理其他文章
- 生成的EPUB文件可以在大多数电子书阅读器中打开

## 依赖库

- `axios` - HTTP请求
- `cheerio` - HTML解析
- `turndown` - HTML转Markdown
- `epub-gen` - EPUB生成
- `fs-extra` - 文件系统操作

## 故障排除

如果遇到问题，请检查：

1. 网络连接是否正常
2. Paul Graham网站是否可以访问
3. 是否有足够的磁盘空间保存文件

## 许可证

MIT License