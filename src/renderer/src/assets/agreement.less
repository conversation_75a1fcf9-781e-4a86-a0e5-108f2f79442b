.container-content-box {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  left: 0;
  z-index: 99999;
  background: rgba(0, 0, 0, 0.6);
  .container {
    width: 840px;
    height: 640px;
    position: relative;
    padding: 30px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    background: #fff;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.25);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #3f4041;

    .close {
      position: absolute;
      top: 16px;
      right: 16px;
      width: 13px;
      cursor: pointer;
    }

    .bold {
      font-weight: bold;
    }
    .page-title {
      font-size: 18px;
      font-family:
        PingFang SC-Medium,
        PingFang SC;
      color: #333333;
      text-align: center;
    }
    ::-webkit-scrollbar {
      width: 5px;
      /* 滚动条的宽度 */
    }

    ::-webkit-scrollbar-track {
      border-radius: 5px;
      background-color: unset;
      /* 轨道的背景颜色 */
    }

    ::-webkit-scrollbar-thumb {
      background-color: #a7a7a7;
      /* 滑块的颜色 */
      border-radius: 5px;
      /* 滑块的圆角 */
    }
    .agreement {
      overflow: auto;
      margin-top: 10px;
      font-size: 12px;
      color: #333333;
      font-weight: 400;
      line-height: 1.5;
      padding: 20px 14px;
      margin-top: 28px;
      background: #F4F4F6;;
      border-radius: 8px;

      .text {
        text-indent: 2em;
      }
      .marg {
        margin-bottom: 10px;
      }
      .bold {
        font-weight: bold;
      }
      .underline {
        text-decoration: underline;
      }
    }

    .agree-btn {
      margin: 22px auto 0;
      padding: 0 10px;
      height: 44px;
      background: #434af9;
      border-radius: 4px 4px 4px 4px;
      font-size: 16px;
      font-weight: 600;
      color: #ffffff;
      line-height: 44px;
      text-align: center;
      cursor: pointer;

      &.disabled {
        background: rgba(67, 74, 249, 0.2);
        cursor: not-allowed;
      }
    }
  }
}
