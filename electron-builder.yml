appId: com.heygem.app
productName: HeyGem
directories:
  buildResources: build
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
asarUnpack:
  - resources/**
win:
  executableName: HeyGem
nsis:
  oneClick: false
  allowElevation: true
  perMachine: true
  allowToChangeInstallationDirectory: true
  artifactName: ${productName}-${version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
  installerIcon: build/icon.ico
  uninstallerIcon: build/icon.ico
mac:
  entitlementsInherit: build/entitlements.mac.plist
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
dmg:
  artifactName: ${productName}-${version}.${ext}
linux:
  target:
    - AppImage
    # - snap
    # - deb
  maintainer: electronjs.org
  category: Utility
  icon: build/icon.png
  desktop:
    Name: ${productName}
    Comment: ${productName}
    GenericName: ${productName}
    executableName: ${productName}
    Icon: build/icon.png
    Terminal: false
    Type: Application
    Categories: Utility
    Keywords:
      - ${productName}
      - 数字人
      - 视频生成
appImage:
  artifactName: ${productName}-${version}.${ext}
npmRebuild: false
publish:
  provider: generic
  url: https://example.com/auto-updates
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
